import os
import json
import fitz
from PIL import Image, ImageDraw

class AppState:
    def __init__(self):
        # 核心状态变量
        self.current_pdf = None
        self.current_json = None
        self.image_directory = None
        self.root_folder = None
        self.zoom_level = 1.0  # Default zoom level

        self.selected_block = None
        self.selected_page = None
        self.json_data = {"pdf_info": []}
        self.pdf_doc = None
        self.current_page_index = 0
        self.total_pages = 0
        self.visible_pages = set()
        
        # UI组件引用
        self.status_text = None
        self.page_counter = None
        self.pdf_view = None
        self.json_editor = None
        self.file_tree = None
        self.progress_bar = None
        
        # 状态监听
        self.listeners = []
        
        # UI控制回调
        self.ui_control_callbacks = []
    
    def add_listener(self, callback):
        """添加状态监听器"""
        if callback not in self.listeners:
            self.listeners.append(callback)
    
    def remove_listener(self, callback):
        """移除状态监听器"""
        if callback in self.listeners:
            self.listeners.remove(callback)
    
    def notify_listeners(self, **kwargs):
        """通知所有监听器状态变化"""
        for callback in self.listeners:
            try:
                callback(** kwargs)
            except Exception as e:
                print(f"状态监听器错误: {e}")
    
    def add_ui_control_callback(self, callback):
        """添加UI控制回调"""
        if callback not in self.ui_control_callbacks:
            self.ui_control_callbacks.append(callback)
    
    def set_processing_mode(self, processing: bool):
        """设置处理模式，禁用/启用UI元素"""
        for callback in self.ui_control_callbacks:
            try:
                callback(processing)
            except Exception as e:
                print(f"UI控制回调错误: {e}")
    
    def set_selected_page(self, page):
        """设置选中页码并通知监听器"""
        self.selected_page = page
        self.notify_listeners(selected_page=page)
    
    def set_selected_block(self, block):
        """设置选中块索引并通知监听器"""
        self.selected_block = block
        self.notify_listeners(selected_block=block)
    
    def reset_all_selections(self):
        """重置所有选中状态"""
        self.selected_page = None
        self.selected_block = None
        self.current_page_index = 0
        self.notify_listeners(selected_page=None, selected_block=None)