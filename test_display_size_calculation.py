#!/usr/bin/env python3
"""
测试显示尺寸计算修复的效果
验证 ImageFit.FIT_WIDTH 模式下的精确显示尺寸计算
"""

import flet as ft

def test_image_fit_calculations():
    """测试不同图片适应模式的尺寸计算"""
    print("🧪 测试图片适应模式尺寸计算...")
    
    # 模拟不同的场景
    test_cases = [
        {
            "name": "标准A4页面",
            "image_width": 1190, "image_height": 1684,  # 595*2, 842*2
            "container_width": 595, "container_height": 842,
            "description": "图片与容器比例相同"
        },
        {
            "name": "宽图片",
            "image_width": 2000, "image_height": 1000,
            "container_width": 600, "container_height": 800,
            "description": "图片比容器更宽"
        },
        {
            "name": "高图片", 
            "image_width": 800, "image_height": 2000,
            "container_width": 600, "container_height": 800,
            "description": "图片比容器更高"
        },
        {
            "name": "小图片",
            "image_width": 300, "image_height": 400,
            "container_width": 600, "container_height": 800,
            "description": "图片比容器小"
        }
    ]
    
    for case in test_cases:
        print(f"\n📊 测试案例: {case['name']} ({case['description']})")
        print(f"   图片尺寸: {case['image_width']} x {case['image_height']}")
        print(f"   容器尺寸: {case['container_width']} x {case['container_height']}")
        
        # 计算图片和容器的宽高比
        image_aspect = case['image_width'] / case['image_height']
        container_aspect = case['container_width'] / case['container_height']
        
        print(f"   图片宽高比: {image_aspect:.3f}")
        print(f"   容器宽高比: {container_aspect:.3f}")
        
        # 模拟FIT_WIDTH计算
        actual_width = case['container_width']
        actual_height = case['container_width'] / image_aspect
        
        print(f"   FIT_WIDTH显示尺寸: {actual_width:.1f} x {actual_height:.1f}")
        
        # 计算偏移
        offset_x = max(0, (case['container_width'] - actual_width) / 2)
        offset_y = max(0, (case['container_height'] - actual_height) / 2)
        
        print(f"   偏移量: x={offset_x:.1f}, y={offset_y:.1f}")
        
        # 计算缩放因子
        scale_x = actual_width / case['image_width']
        scale_y = actual_height / case['image_height']
        
        print(f"   缩放因子: x={scale_x:.3f}, y={scale_y:.3f}")
        
        # 检查是否超出容器
        if actual_height > case['container_height']:
            print(f"   ⚠️  显示高度超出容器 {actual_height - case['container_height']:.1f} 像素")
        else:
            print(f"   ✅ 显示尺寸在容器范围内")

def test_coordinate_transformation():
    """测试坐标转换的准确性"""
    print("\n🧪 测试坐标转换准确性...")
    
    # 模拟参数
    pdf_page_width = 595.0
    pdf_page_height = 842.0
    image_scale = 2.0
    zoom_level = 1.0
    
    # 图片尺寸
    image_width = pdf_page_width * image_scale  # 1190
    image_height = pdf_page_height * image_scale  # 1684
    
    # 容器尺寸
    container_width = pdf_page_width * zoom_level  # 595
    container_height = pdf_page_height * zoom_level  # 842
    
    # FIT_WIDTH显示尺寸计算
    actual_display_width = container_width  # 595
    actual_display_height = container_width / (image_width / image_height)  # 595 / (1190/1684) = 842
    
    # 偏移计算
    offset_x = max(0, (container_width - actual_display_width) / 2)  # 0
    offset_y = max(0, (container_height - actual_display_height) / 2)  # 0
    
    # 缩放因子
    display_scale_x = actual_display_width / image_width  # 595/1190 = 0.5
    display_scale_y = actual_display_height / image_height  # 842/1684 = 0.5
    
    print(f"📏 PDF页面: {pdf_page_width} x {pdf_page_height}")
    print(f"📏 图片生成: {image_width} x {image_height}")
    print(f"📏 容器尺寸: {container_width} x {container_height}")
    print(f"📏 显示尺寸: {actual_display_width:.1f} x {actual_display_height:.1f}")
    print(f"📏 偏移量: {offset_x:.1f}, {offset_y:.1f}")
    print(f"📏 缩放因子: {display_scale_x:.3f}, {display_scale_y:.3f}")
    
    # 测试bbox转换
    test_bboxes = [
        [100, 200, 300, 250],  # 标准矩形
        [0, 0, 595, 50],       # 页面顶部横条
        [0, 792, 595, 842],    # 页面底部横条
        [50, 50, 100, 100],    # 小正方形
    ]
    
    print(f"\n📦 bbox坐标转换测试:")
    for i, bbox in enumerate(test_bboxes):
        x0, y0, x1, y1 = bbox
        
        # 转换后的坐标
        left = x0 * image_scale * display_scale_x + offset_x
        top = y0 * image_scale * display_scale_y + offset_y
        width = (x1 - x0) * image_scale * display_scale_x
        height = (y1 - y0) * image_scale * display_scale_y
        
        print(f"   bbox{i+1}: {bbox} → left={left:.1f}, top={top:.1f}, width={width:.1f}, height={height:.1f}")
        
        # 验证简化情况（当比例相同时，应该直接等于原坐标）
        if abs(display_scale_x - 0.5) < 0.001 and offset_x == 0 and offset_y == 0:
            expected_left = x0 * 0.5
            expected_top = y0 * 0.5
            if abs(left - expected_left) < 0.1 and abs(top - expected_top) < 0.1:
                print(f"      ✅ 坐标转换正确")
            else:
                print(f"      ❌ 坐标转换错误，预期 left={expected_left}, top={expected_top}")

def test_edge_cases():
    """测试边缘情况"""
    print("\n🧪 测试边缘情况...")
    
    edge_cases = [
        {
            "name": "极宽图片",
            "image_width": 3000, "image_height": 500,
            "container_width": 600, "container_height": 800
        },
        {
            "name": "极高图片",
            "image_width": 500, "image_height": 3000,
            "container_width": 600, "container_height": 800
        },
        {
            "name": "正方形图片",
            "image_width": 1000, "image_height": 1000,
            "container_width": 600, "container_height": 800
        }
    ]
    
    for case in edge_cases:
        print(f"\n📊 {case['name']}:")
        
        # FIT_WIDTH计算
        actual_width = case['container_width']
        actual_height = case['container_width'] / (case['image_width'] / case['image_height'])
        
        # 偏移计算
        offset_y = max(0, (case['container_height'] - actual_height) / 2)
        
        print(f"   显示尺寸: {actual_width:.1f} x {actual_height:.1f}")
        print(f"   垂直偏移: {offset_y:.1f}")
        
        if actual_height > case['container_height']:
            print(f"   ⚠️  图片高度超出容器 {actual_height - case['container_height']:.1f} 像素")
        elif actual_height < case['container_height']:
            print(f"   ℹ️  图片高度小于容器，有 {case['container_height'] - actual_height:.1f} 像素留白")
        else:
            print(f"   ✅ 图片完全填充容器")

if __name__ == "__main__":
    print("🚀 开始显示尺寸计算测试\n")
    
    test_image_fit_calculations()
    test_coordinate_transformation()
    test_edge_cases()
    
    print("\n🎉 显示尺寸计算测试完成！")
    print("\n📝 关键发现:")
    print("  1. FIT_WIDTH模式下图片宽度总是等于容器宽度")
    print("  2. 图片高度按比例缩放，可能产生垂直偏移")
    print("  3. 坐标转换需要考虑显示缩放因子和偏移量")
    print("  4. 不同宽高比的图片会产生不同的显示效果")
