#!/usr/bin/env python3
"""
简单的图片生成一致性测试
"""

import fitz
import tempfile
import os

def test_image_generation_consistency():
    """测试图片生成参数的一致性"""
    print("🧪 测试图片生成参数一致性...")
    
    # 创建简单的测试PDF
    doc = fitz.open()
    page = doc.new_page(width=595, height=842)  # A4
    page.insert_text((50, 50), "Test Page", fontsize=12)
    
    # 保存到临时文件
    pdf_path = tempfile.mktemp(suffix='.pdf')
    doc.save(pdf_path)
    doc.close()
    
    try:
        # 重新打开PDF进行测试
        doc = fitz.open(pdf_path)
        page = doc.load_page(0)
        
        print(f"📏 PDF页面尺寸: {page.rect.width} x {page.rect.height}")
        print(f"📏 MediaBox: {page.mediabox}")
        print(f"📏 CropBox: {page.cropbox}")
        
        # 测试两种生成方式
        PDF_IMAGE_SCALE = 2.0
        
        # 方式1：PDFProcessor风格（带MediaBox裁剪）
        pix1 = page.get_pixmap(
            matrix=fitz.Matrix(PDF_IMAGE_SCALE, PDF_IMAGE_SCALE),
            clip=page.mediabox,
            alpha=False
        )
        
        # 方式2：旧的PDFViewer风格（无MediaBox裁剪）
        pix2 = page.get_pixmap(
            matrix=fitz.Matrix(PDF_IMAGE_SCALE, PDF_IMAGE_SCALE)
        )
        
        print(f"\n🖼️  方式1 (带MediaBox): {pix1.width} x {pix1.height}")
        print(f"🖼️  方式2 (无MediaBox): {pix2.width} x {pix2.height}")
        
        # 检查一致性
        if pix1.width == pix2.width and pix1.height == pix2.height:
            print("✅ 两种方式生成的图片尺寸一致")
            consistent = True
        else:
            print("❌ 两种方式生成的图片尺寸不一致")
            print(f"   差异: 宽度差 {abs(pix1.width - pix2.width)}, 高度差 {abs(pix1.height - pix2.height)}")
            consistent = False
        
        # 验证与页面尺寸的关系
        expected_width = page.rect.width * PDF_IMAGE_SCALE
        expected_height = page.rect.height * PDF_IMAGE_SCALE
        
        print(f"\n📊 预期图片尺寸: {expected_width} x {expected_height}")
        print(f"📊 方式1实际尺寸: {pix1.width} x {pix1.height}")
        print(f"📊 方式2实际尺寸: {pix2.width} x {pix2.height}")
        
        # 检查是否符合预期
        if (pix1.width == expected_width and pix1.height == expected_height):
            print("✅ 方式1符合预期尺寸")
        else:
            print("❌ 方式1不符合预期尺寸")
            
        if (pix2.width == expected_width and pix2.height == expected_height):
            print("✅ 方式2符合预期尺寸")
        else:
            print("❌ 方式2不符合预期尺寸")
        
        doc.close()
        return consistent
        
    finally:
        # 清理临时文件
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

def test_coordinate_calculation():
    """测试坐标转换计算"""
    print("\n🧪 测试坐标转换计算...")
    
    # 模拟参数
    pdf_page_width = 595.0
    pdf_page_height = 842.0
    image_scale = 2.0
    zoom_level = 1.0
    
    # 模拟bbox
    bbox = [100, 200, 300, 250]  # x0, y0, x1, y1
    x0, y0, x1, y1 = bbox
    
    # 计算图片尺寸
    image_width = pdf_page_width * image_scale
    image_height = pdf_page_height * image_scale
    
    # 计算显示容器尺寸
    container_width = pdf_page_width * zoom_level
    container_height = pdf_page_height * zoom_level
    
    # 计算显示缩放因子
    display_scale_x = container_width / image_width
    display_scale_y = container_height / image_height
    
    # 坐标转换
    left = x0 * image_scale * display_scale_x
    top = y0 * image_scale * display_scale_y
    width = (x1 - x0) * image_scale * display_scale_x
    height = (y1 - y0) * image_scale * display_scale_y
    
    print(f"📏 PDF页面尺寸: {pdf_page_width} x {pdf_page_height}")
    print(f"📏 图片生成尺寸: {image_width} x {image_height}")
    print(f"📏 显示容器尺寸: {container_width} x {container_height}")
    print(f"📏 显示缩放因子: {display_scale_x:.3f} x {display_scale_y:.3f}")
    print(f"📦 原始bbox: {bbox}")
    print(f"📦 转换后位置: left={left:.1f}, top={top:.1f}, width={width:.1f}, height={height:.1f}")
    
    # 验证简化情况（zoom_level=1.0时）
    if zoom_level == 1.0:
        # 此时 display_scale 应该是 0.5 (因为 container=595, image=1190)
        expected_scale = 0.5
        if abs(display_scale_x - expected_scale) < 0.001:
            print("✅ 缩放因子计算正确")
        else:
            print(f"❌ 缩放因子计算错误，预期 {expected_scale}，实际 {display_scale_x}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始简单图片生成一致性测试\n")
    
    success1 = test_image_generation_consistency()
    success2 = test_coordinate_calculation()
    
    if success1 and success2:
        print("\n🎉 测试完成！")
        print("\n📝 关键发现:")
        print("  1. MediaBox裁剪参数对图片尺寸的影响")
        print("  2. 坐标转换中的缩放因子计算")
        print("  3. 统一图片生成参数的重要性")
    else:
        print("\n⚠️  发现问题，需要进一步分析")
