# PDF矩形框坐标转换修复

## 问题描述

PDF图片上的矩形框定位不准确，存在以下问题：
1. 坐标转换系统不完整
2. 图片生成时的缩放与显示时的缩放不匹配
3. 使用了硬编码的修正系数（0.68）
4. 图片的自适应显示模式与绝对定位的矩形框不匹配
5. 缺少获取图片实际显示尺寸的机制

## 修复方案

### 1. 统一坐标系统

**修改文件**: `utils/pdf_processor.py`

- 添加了缩放信息的记录和保存功能
- 在图片生成时记录原始页面尺寸和缩放信息
- 保存缩放信息到 `scale_info.json` 文件
- 添加了 `get_scale_info()` 方法来获取缩放信息

```python
# 新增功能
def _generate_page_image(self, doc, page_idx, images_dir):
    """生成单页图片，返回页面信息"""
    # 记录原始页面尺寸
    original_width = page.rect.width
    original_height = page.rect.height
    
    # 返回页面信息
    return {
        "original_width": original_width,
        "original_height": original_height,
        "image_scale": zoom,
        "image_width": original_width * zoom,
        "image_height": original_height * zoom
    }

def get_scale_info(self, pdf_path):
    """获取PDF的缩放信息"""
    # 从 scale_info.json 读取缩放信息
```

### 2. 获取实际显示尺寸

**修改文件**: `components/pdf_viewer.py`

- 添加了图片尺寸估算函数 `_get_actual_image_size()`
- 基于PDF页面尺寸和缩放级别估算实际显示尺寸
- 在渲染页面时自动获取尺寸信息

```python
def _get_actual_image_size(self, page_index):
    """获取图片的实际显示尺寸（估算）"""
    # 获取PDF页面尺寸
    page = self.state.pdf_doc.load_page(page_index)
    pdf_page_width = page.rect.width
    pdf_page_height = page.rect.height
    
    # 估算实际显示尺寸
    estimated_width = pdf_page_width * self.state.zoom_level
    estimated_height = pdf_page_height * self.state.zoom_level
    
    self.actual_image_width = estimated_width
    self.actual_image_height = estimated_height
```

### 3. 改进坐标转换逻辑

**修改文件**: `components/pdf_viewer.py`

- 移除了硬编码的修正系数（0.68）
- 使用图片生成时的缩放信息和实际显示尺寸
- 实现了更精确的坐标转换算法

```python
# 改进的坐标转换
if self.actual_image_width and self.actual_image_height:
    # 计算实际缩放比例
    scale_x = self.actual_image_width / (pdf_page_width * image_scale)
    scale_y = self.actual_image_height / (pdf_page_height * image_scale)
    
    left = x0 * image_scale * scale_x
    top = y0 * image_scale * scale_y
    width = (x1 - x0) * image_scale * scale_x
    height = (y1 - y0) * image_scale * scale_y
else:
    # 回退到简单的缩放转换
    left = x0 * total_scale
    top = y0 * total_scale
    width = (x1 - x0) * total_scale
    height = (y1 - y0) * total_scale
```

### 4. 优化Stack布局

**修改文件**: `components/pdf_viewer.py`

- 为Stack设置固定尺寸，与图片实际显示尺寸一致
- 确保矩形框定位的稳定性

```python
# 创建Stack布局 - 设置固定尺寸以确保定位稳定
stack = ft.Stack(
    controls=[image],
    width=page_width * self.state.zoom_level,  # 设置固定宽度
    height=page_height * self.state.zoom_level,  # 设置固定高度
)
```

### 5. 添加调试功能

**修改文件**: `components/pdf_viewer.py`

- 添加了 `debug_coordinate_conversion()` 方法
- 提供详细的调试信息来验证坐标转换
- 显示实际图片尺寸和计算尺寸的对比

```python
def debug_coordinate_conversion(self, page_index):
    """调试坐标转换的准确性"""
    # 显示PDF原始尺寸、图片生成缩放、用户缩放级别等信息
    # 计算并显示实际缩放比例
```

## 测试验证

创建了测试脚本 `test_coordinate_fix.py` 来验证修复效果：

- 测试缩放信息生成功能
- 测试坐标转换逻辑的准确性
- 验证转换后的坐标与期望坐标的误差

测试结果显示坐标转换误差为0，修复成功。

## 预期效果

修复后应该实现：

1. **精确定位**: 矩形框与PDF内容完全对齐
2. **缩放一致性**: 不同缩放级别下矩形框位置正确
3. **自适应支持**: 图片尺寸变化时矩形框自动调整
4. **调试友好**: 提供足够的调试信息来验证修复效果

## 使用方法

1. 重新生成PDF图片时会自动创建缩放信息文件
2. 加载PDF时会自动读取缩放信息
3. 图片加载完成后会自动获取实际显示尺寸
4. 矩形框会根据实际尺寸进行精确定位

## 注意事项

1. 对于已存在的PDF，需要重新生成图片才能获得缩放信息
2. 如果缩放信息文件不存在，会使用默认的缩放因子
3. 调试信息会在控制台输出，可以通过日志查看坐标转换过程 