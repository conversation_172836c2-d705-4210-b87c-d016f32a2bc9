import flet as ft
from enum import IntEnum
from typing import Optional, Dict, Any
import asyncio

class StatusType(IntEnum):
    PROCESSING = 3    # 处理中状态（最高优先级）
    SYSTEM = 2      # 系统状态
    USER_ACTION = 1  # 用户操作状态
    IDLE = 0        # 空闲状态（最低优先级）

class StatusManager:
    def __init__(self, status_text: ft.Text, progress_bar: ft.ProgressBar, page: ft.Page = None):
        self.status_text = status_text
        self.progress_bar = progress_bar
        self.page = page
        self.current_status: Dict[str, Any] = {
            'type': StatusType.IDLE,
            'message': '就绪',
            'color': ft.Colors.BLUE,
            'persistent': False
        }
        self.status_stack: list = []
        self._lock = asyncio.Lock()
        self._max_stack_size = 50  # 限制状态栈大小
    
    async def set_status(self, message: str, status_type: StatusType = StatusType.SYSTEM, 
                        color: Optional[str] = None, persistent: bool = False):
        """设置状态文本，带优先级管理"""
        async with self._lock:
            # 如果新状态优先级更高或相等，则更新
            if status_type >= self.current_status['type']:
                old_status = self.current_status.copy()
                
                self.current_status.update({
                    'type': status_type,
                    'message': message,
                    'color': color or self.current_status['color'],
                    'persistent': persistent
                })
                
                self.status_text.value = message
                self.status_text.color = self.current_status['color']
                
                # 如果不是持久状态，保存到栈中以便恢复
                if not persistent and status_type > StatusType.IDLE:
                    self.status_stack.append(old_status)
                
                # 刷新UI
                if self.page:
                    self.page.update()
    
    async def set_processing_status(self, message: str, progress: Optional[float] = None):
        """设置处理状态，自动显示进度条"""
        await self.set_status(message, StatusType.PROCESSING, ft.Colors.BLUE)
        
        if progress is not None:
            self.progress_bar.visible = True
            self.progress_bar.value = progress
        else:
            self.progress_bar.visible = True
            self.progress_bar.value = None  # 不确定进度
        
        # 刷新UI
        if self.page:
            self.page.update()
    
    async def update_processing_progress(self, current: int, total: int):
        """更新处理进度"""
        async with self._lock:
            if self.current_status['type'] == StatusType.PROCESSING:
                message = f"正在处理PDF图片... {current}/{total}"
                self.status_text.value = message
                self.progress_bar.value = current / max(1, total)
                self.status_text.color = self.current_status['color']  # 确保颜色一致
                
                # 刷新UI
                if self.page:
                    self.page.update()
    
    async def complete_processing(self):
        """完成处理，隐藏并重置进度条"""
        self.progress_bar.visible = False
        self.progress_bar.value = 0  # 重置进度值
        # 不自动设置状态，由调用者决定最终状态
        
        # 刷新UI
        if self.page:
            self.page.update()
    
    async def set_user_action_status(self, message: str, color: str = ft.Colors.BLUE):
        """设置用户操作状态"""
        await self.set_status(message, StatusType.USER_ACTION, color)
        # set_status已经包含page.update()调用
    
    async def set_system_status(self, message: str, color: str = ft.Colors.BLUE):
        """设置系统状态"""
        await self.set_status(message, StatusType.SYSTEM, color)
    
    async def restore_previous_status(self):
        """恢复上一个非持久状态"""
        async with self._lock:
            if self.status_stack and self.current_status['type'] > StatusType.IDLE:
                previous_status = self.status_stack.pop()
                self.current_status = previous_status
                self.status_text.value = previous_status['message']
                self.status_text.color = previous_status['color']
                
                # 同步进度条状态
                if previous_status['type'] != StatusType.PROCESSING:
                    self.progress_bar.visible = False
                    self.progress_bar.value = 0
            elif not self.status_stack and self.current_status['type'] > StatusType.IDLE:
                # 栈为空时强制回到空闲状态
                await self.force_idle()
            
            # 刷新UI
            if self.page:
                self.page.update()
    
    async def force_idle(self):
        """强制设置为空闲状态"""
        async with self._lock:
            await self.set_status('就绪', StatusType.IDLE, ft.Colors.BLUE)
            self.progress_bar.visible = False
            self.progress_bar.value = 0
            self.status_stack.clear()
            # set_status已经包含page.update()调用
    
    def validate_state(self) -> bool:
        """验证当前状态一致性"""
        return (
            self.current_status['type'] in StatusType and
            0 <= self.current_status['type'] <= 3 and
            isinstance(self.status_text.value, str) and
            self.progress_bar.value is None or 0 <= self.progress_bar.value <= 1
        )