#!/usr/bin/env python3
"""
测试坐标转换修复的效果
验证修复后的坐标转换公式是否正确
"""

def test_coordinate_transformation():
    """测试坐标转换修复"""
    print("🧪 测试坐标转换修复效果")
    
    # 模拟你的实际数据
    image_width = 2480
    image_height = 3544
    image_scale = 2.0
    
    container_width = 1240.0
    container_height = 1772.0
    
    # 计算显示缩放因子
    display_scale_x = container_width / image_width  # 1240 / 2480 = 0.5
    display_scale_y = container_height / image_height  # 1772 / 3544 = 0.5
    
    # 偏移量（在这个例子中应该是0）
    offset_x = 0.0
    offset_y = 0.0
    
    print(f"📊 参数:")
    print(f"  图片尺寸: {image_width} x {image_height}")
    print(f"  图片缩放: {image_scale}")
    print(f"  容器尺寸: {container_width} x {container_height}")
    print(f"  显示缩放因子: x={display_scale_x:.3f}, y={display_scale_y:.3f}")
    print(f"  偏移量: x={offset_x}, y={offset_y}")
    
    # 测试bbox转换
    test_bboxes = [
        [177, 355, 1067, 673],
        [550, 1469, 1067, 1600]
    ]
    
    print(f"\n🔧 坐标转换对比:")
    
    for i, bbox in enumerate(test_bboxes):
        x0, y0, x1, y1 = bbox
        
        print(f"\n📦 bbox {i+1}: {bbox}")
        
        # 错误的转换（修复前）
        left_wrong = x0 * image_scale * display_scale_x + offset_x
        top_wrong = y0 * image_scale * display_scale_y + offset_y
        width_wrong = (x1 - x0) * image_scale * display_scale_x
        height_wrong = (y1 - y0) * image_scale * display_scale_y
        
        print(f"  ❌ 修复前 (错误): left={left_wrong:.1f}, top={top_wrong:.1f}, width={width_wrong:.1f}, height={height_wrong:.1f}")
        
        # 正确的转换（修复后）
        left_correct = x0 * display_scale_x + offset_x
        top_correct = y0 * display_scale_y + offset_y
        width_correct = (x1 - x0) * display_scale_x
        height_correct = (y1 - y0) * display_scale_y
        
        print(f"  ✅ 修复后 (正确): left={left_correct:.1f}, top={top_correct:.1f}, width={width_correct:.1f}, height={height_correct:.1f}")
        
        # 计算差异
        diff_left = abs(left_wrong - left_correct)
        diff_top = abs(top_wrong - top_correct)
        
        print(f"  📏 位置差异: left差{diff_left:.1f}, top差{diff_top:.1f}")
        
        # 分析影响
        if diff_left > 10 or diff_top > 10:
            print(f"  ⚠️  差异较大，会导致明显的定位偏差")
        else:
            print(f"  ℹ️  差异较小")

def test_different_scenarios():
    """测试不同场景下的坐标转换"""
    print(f"\n🧪 测试不同场景的坐标转换")
    
    scenarios = [
        {
            "name": "标准情况 (缩放1.0)",
            "image_scale": 2.0,
            "zoom_level": 1.0,
            "pdf_width": 1240,
            "pdf_height": 1772
        },
        {
            "name": "放大情况 (缩放1.5)",
            "image_scale": 2.0,
            "zoom_level": 1.5,
            "pdf_width": 1240,
            "pdf_height": 1772
        },
        {
            "name": "缩小情况 (缩放0.5)",
            "image_scale": 2.0,
            "zoom_level": 0.5,
            "pdf_width": 1240,
            "pdf_height": 1772
        }
    ]
    
    test_bbox = [100, 200, 300, 250]  # 简单的测试bbox
    
    for scenario in scenarios:
        print(f"\n📊 {scenario['name']}:")
        
        # 计算参数
        image_width = scenario['pdf_width'] * scenario['image_scale']
        image_height = scenario['pdf_height'] * scenario['image_scale']
        container_width = scenario['pdf_width'] * scenario['zoom_level']
        container_height = scenario['pdf_height'] * scenario['zoom_level']
        
        display_scale_x = container_width / image_width
        display_scale_y = container_height / image_height
        
        print(f"  容器尺寸: {container_width} x {container_height}")
        print(f"  显示缩放: x={display_scale_x:.3f}, y={display_scale_y:.3f}")
        
        # 转换测试bbox
        x0, y0, x1, y1 = test_bbox
        left = x0 * display_scale_x
        top = y0 * display_scale_y
        width = (x1 - x0) * display_scale_x
        height = (y1 - y0) * display_scale_x
        
        print(f"  bbox {test_bbox} → left={left:.1f}, top={top:.1f}, width={width:.1f}, height={height:.1f}")

def explain_coordinate_systems():
    """解释坐标系统的转换"""
    print(f"\n📚 坐标系统转换说明:")
    print(f"")
    print(f"🎯 三个坐标系统:")
    print(f"  1. PDF坐标系: PDF文档中的原始坐标 (单位: 点)")
    print(f"  2. 图片坐标系: 生成图片的像素坐标 (PDF坐标 × 图片缩放)")
    print(f"  3. 显示坐标系: 屏幕上的显示坐标 (单位: 像素)")
    print(f"")
    print(f"🔧 正确的转换路径:")
    print(f"  PDF坐标 → 显示坐标 (直接转换)")
    print(f"  公式: 显示坐标 = PDF坐标 × 显示缩放因子")
    print(f"")
    print(f"❌ 错误的转换路径 (修复前):")
    print(f"  PDF坐标 → 图片坐标 → 显示坐标 (多余的转换)")
    print(f"  公式: 显示坐标 = PDF坐标 × 图片缩放 × 显示缩放因子")
    print(f"")
    print(f"💡 关键理解:")
    print(f"  显示缩放因子 = 容器尺寸 / 图片尺寸")
    print(f"  这个因子已经包含了从PDF到显示的完整转换关系")
    print(f"  不需要再乘以图片缩放因子")

if __name__ == "__main__":
    print("🚀 开始坐标转换修复测试\n")
    
    test_coordinate_transformation()
    test_different_scenarios()
    explain_coordinate_systems()
    
    print(f"\n🎉 测试完成！")
    print(f"\n📝 修复总结:")
    print(f"  ✅ 移除了多余的 image_scale 乘法")
    print(f"  ✅ 使用直接的 PDF → 显示 坐标转换")
    print(f"  ✅ 显示缩放因子已包含完整转换关系")
    print(f"  ✅ 矩形框定位应该现在准确了")
