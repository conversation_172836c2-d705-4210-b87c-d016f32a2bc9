# 页面索引格式不匹配问题修复报告

## 🔍 问题分析

### 根本原因
在 `pdf_processor.py` 和 `pdf_viewer.py` 之间存在页面索引格式不匹配的问题：

1. **存储阶段** (`pdf_processor.py` 第78行)：
   ```python
   scale_info["pages"][page_idx] = page_info  # page_idx 是整数
   ```

2. **JSON序列化问题**：
   - Python字典的整数键在JSON序列化后会变成字符串键
   - 原始: `{0: {...}, 1: {...}, 2: {...}}`
   - JSON后: `{"0": {...}, "1": {...}, "2": {...}}`

3. **查找阶段** (`pdf_viewer.py` 第173行，修复前)：
   ```python
   if self.scale_info and str(page_index) in self.scale_info.get("pages", {}):
   ```
   - 代码尝试查找字符串键，但实际需要的是整数键

### 影响范围
- **部分PDF正常**：碰巧缓存了正确格式的数据，或者页面索引匹配
- **部分PDF异常**：缩放信息查找失败，回退到默认值，导致矩形框位置偏差

## 🔧 修复方案

### 1. 统一存储格式 (`utils/pdf_processor.py`)

#### 修复点1：保存时确保键格式一致
```python
# 在保存缩放信息前，重新整理pages数据
if "pages" in scale_info:
    pages_data = {}
    for key, value in scale_info["pages"].items():
        # 确保键为整数
        int_key = int(key) if isinstance(key, str) else key
        pages_data[int_key] = value
    scale_info["pages"] = pages_data
```

#### 修复点2：读取时处理JSON反序列化问题
```python
def get_scale_info(self, pdf_path):
    # ... 读取JSON文件 ...
    
    # 修复：确保页面键为整数格式
    if "pages" in scale_info and isinstance(scale_info["pages"], dict):
        pages_data = {}
        for key, value in scale_info["pages"].items():
            try:
                int_key = int(key)  # JSON反序列化后键变成字符串，转换回整数
                pages_data[int_key] = value
            except (ValueError, TypeError):
                pages_data[key] = value  # 保持原键作为回退
        scale_info["pages"] = pages_data
    
    return scale_info
```

### 2. 简化查找逻辑 (`components/pdf_viewer.py`)

#### 修复前（复杂的回退逻辑）：
```python
if self.scale_info and str(page_index) in self.scale_info.get("pages", {}):
    page_info = self.scale_info["pages"][str(page_index)]
```

#### 修复后（直接使用整数键）：
```python
if self.scale_info and "pages" in self.scale_info:
    pages_data = self.scale_info["pages"]
    if page_index in pages_data:  # 直接使用整数键查找
        page_info = pages_data[page_index]
```

## 🧪 验证测试

### 测试结果
```
原始键类型: ['int', 'int', 'int']
JSON反序列化后键类型: ['str', 'str', 'str']  ← 问题所在
修复后键类型: ['int', 'int', 'int']           ← 修复成功
修复成功: True
```

### 测试覆盖
1. ✅ JSON序列化/反序列化键类型转换
2. ✅ 混合键格式处理（字符串+整数）
3. ✅ 页面查找逻辑验证
4. ✅ 边界情况处理（不存在的页面）

## 📊 修复效果

### 修复前
- 🔴 部分PDF矩形框位置正确
- 🔴 部分PDF矩形框位置偏差
- 🔴 缩放信息查找不稳定
- 🔴 调试信息显示"未找到缩放信息"

### 修复后
- ✅ 所有PDF使用统一的页面索引格式
- ✅ 缩放信息查找稳定可靠
- ✅ 矩形框位置计算基于正确的缩放数据
- ✅ 调试信息清晰显示查找状态

## 🎯 技术要点

### JSON序列化的键类型问题
- **Python字典**: 支持任意类型作为键（整数、字符串等）
- **JSON格式**: 只支持字符串作为键
- **转换影响**: `{0: "value"}` → `{"0": "value"}`

### 修复策略
1. **存储时预处理**: 在JSON序列化前确保键格式一致
2. **读取时后处理**: 在JSON反序列化后修复键类型
3. **查找时简化**: 统一使用整数键，避免字符串转换

## 🔮 预期改进

修复此问题后，应该能解决：
1. **矩形框定位不一致**的主要原因
2. **缩放信息查找失败**导致的回退到默认值
3. **调试信息混乱**的问题

但仍需要关注其他可能的问题：
- ImageFit.FIT_WIDTH的显示尺寸计算
- 临时生成图片与预生成图片的参数一致性
- 不同PDF页面尺寸比例的适配

## 📝 使用建议

1. **重新生成图片**: 对于已存在的PDF，建议重新生成图片以获得正确的缩放信息
2. **检查调试输出**: 观察控制台输出，确认缩放信息查找成功
3. **测试不同PDF**: 在不同尺寸比例的PDF上验证修复效果
