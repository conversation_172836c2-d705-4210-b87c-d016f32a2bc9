import asyncio
import flet as ft

class DialogManager:
    @staticmethod
    async def get_text_input(page, title, prompt, initial_value=""):
        result = ft.TextField(label=prompt, value=initial_value)
        dlg = ft.AlertDialog(
            title=ft.Text(title),
            content=result,
            actions=[
                ft.TextButton("确定", on_click=lambda e: page._close_dialog(e, True)),
                ft.TextButton("取消", on_click=lambda e: page._close_dialog(e, False))
            ]
        )
        
        page.dialog = dlg
        dlg.open = True
        page.update()
        
        # 等待对话框关闭
        page._dialog_result = None
        while page._dialog_result is None:
            await asyncio.sleep(0.1)
        
        dlg.open = False
        page.update()
        
        if page._dialog_result:
            return result.value
        return None
    
    @staticmethod
    async def confirm_action(page, message):
        dlg = ft.AlertDialog(
            title=ft.Text("确认操作"),
            content=ft.Text(message),
            actions=[
                ft.TextButton("是", on_click=lambda e: page._close_dialog(e, True)),
                ft.TextButton("否", on_click=lambda e: page._close_dialog(e, False))
            ]
        )
        
        page.dialog = dlg
        dlg.open = True
        page.update()
        
        page._dialog_result = None
        while page._dialog_result is None:
            await asyncio.sleep(0.1)
        
        dlg.open = False
        page.update()
        
        return page._dialog_result