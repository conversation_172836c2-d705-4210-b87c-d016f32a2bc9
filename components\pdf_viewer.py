import asyncio
import fitz
import flet as ft
import os
from utils.pdf_processor import PDFProcessor

class PDFViewerComponent:
    def __init__(self, page, state):
        self.page = page
        self.state = state
        self.view = ft.Column(expand=True, spacing=0, scroll=ft.ScrollMode.AUTO)
        self.bbox_containers = []  # 存储当前页的bbox容器
        self.pdf_processor = PDFProcessor()
        self.processing_mode = False
        self.scale_info = None

    def create(self):
        # 注册UI控制回调
        self.state.add_ui_control_callback(self._set_processing_mode)
        
        # 创建导航按钮引用
        self.prev_btn = ft.IconButton(ft.Icons.NAVIGATE_BEFORE, on_click=self.prev_page)
        self.next_btn = ft.IconButton(ft.Icons.NAVIGATE_NEXT, on_click=self.next_page)
        self.zoom_out_btn = ft.IconButton(ft.Icons.ZOOM_OUT, on_click=self.zoom_out)
        self.zoom_in_btn = ft.IconButton(ft.Icons.ZOOM_IN, on_click=self.zoom_in)
        
        return ft.Container(
            content=ft.Column(
                expand=False,
                alignment=ft.MainAxisAlignment.START,
                horizontal_alignment=ft.CrossAxisAlignment.START,
                controls=[
                    ft.Row(
                        controls=[
                            self.prev_btn,
                            self.state.page_counter,
                            self.next_btn,
                            self.zoom_out_btn,
                            self.zoom_in_btn,
                        ],
                        alignment=ft.MainAxisAlignment.CENTER
                    ),
                    ft.Container(
                        content=self.view,
                        expand=True,
                        border=ft.border.all(1, ft.Colors.GREY_400),
                        padding=0,
                        border_radius=5,
                        margin=0
                    )
                ]
            ),
            expand=True,
            alignment=ft.alignment.top_left
        )
    
    async def load_pdf(self, pdf_path):
        # 强制重置所有相关状态
        self.state.current_pdf = pdf_path
        self.state.reset_all_selections()
        
        # 重新打开PDF文档
        self.state.pdf_doc = await asyncio.to_thread(fitz.open, pdf_path)
        self.state.total_pages = self.state.pdf_doc.page_count
        
        # 获取缩放信息
        self.scale_info = self.pdf_processor.get_scale_info(pdf_path)
        if self.scale_info:
            print(f"DEBUG: 加载缩放信息成功，包含 {len(self.scale_info.get('pages', {}))} 页")

            # 🔧 增强缩放信息回退：检查并修复缺失的缩放信息
            missing_pages = []
            for i in range(self.state.total_pages):
                if not self._validate_and_repair_scale_info(i):
                    missing_pages.append(i)

            if missing_pages:
                print(f"DEBUG: 发现 {len(missing_pages)} 页缺少有效缩放信息: {missing_pages[:10]}{'...' if len(missing_pages) > 10 else ''}")
                print("DEBUG: 将在渲染时使用智能回退机制")

                # 🔧 诊断缩放信息问题
                self.diagnose_scale_info_issue()

                # 🔧 自动修复缺失的缩放信息
                print("DEBUG: 尝试自动修复缺失的缩放信息...")
                repaired = self.repair_missing_scale_info()
                if repaired:
                    print("DEBUG: ✅ 缩放信息修复完成")
                else:
                    print("DEBUG: ⚠️  部分缩放信息无法自动修复，将使用默认值")
        else:
            print("DEBUG: 警告 - 未找到缩放信息文件，将使用智能回退机制")
        
        # 强制清除视图控件和矩形框
        self.view.controls.clear()
        self.bbox_containers = []
        
        # 确保使用最新的JSON数据
        if hasattr(self.state, 'json_data') and self.state.json_data:
            self.render_page(0)
            self.draw_bboxes(0)
        else:
            # 如果没有JSON数据，只渲染页面
            self.render_page(0)

    def render_page(self, page_index):
        self.view.controls.clear()
        self.bbox_containers = []
        
        if not self.state.pdf_doc or page_index >= self.state.total_pages:
            return
        
        # 获取预生成的图片路径
        img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_index)
        
        if not os.path.exists(img_path):
            # 🔧 修复：使用统一的图片生成方法
            page_info = self._generate_page_image_unified(page_index, img_path)

            if page_info:
                # 确保缩放信息存在
                if not hasattr(self, 'scale_info') or not self.scale_info:
                    from utils.pdf_processor import PDF_IMAGE_SCALE
                    self.scale_info = {"pdf_image_scale": PDF_IMAGE_SCALE, "pages": {}}

                if "pages" not in self.scale_info:
                    self.scale_info["pages"] = {}

                # 记录临时生成图片的信息
                self.scale_info["pages"][page_index] = page_info
                print(f"DEBUG: 临时生成图片信息已更新到缓存")
            else:
                print(f"ERROR: 临时图片生成失败，可能影响矩形框定位")
        
        # 获取PDF页面尺寸
        page = self.state.pdf_doc.load_page(page_index)
        page_width = page.rect.width
        page_height = page.rect.height
        
        # 创建图片控件 - 宽度适应容器，高度按比例
        image = ft.Image(
            src=img_path,
            key=f"img_{page_index}_{self.state.zoom_level}",
            fit=ft.ImageFit.FIT_WIDTH,  # 适应宽度，高度按比例调整
            gapless_playback=True
        )
        pdf_page_width = page.rect.width
        pdf_page_height = page.rect.height
        # 创建Stack布局 - 设置固定尺寸以确保定位稳定
        stack = ft.Stack(
            controls=[image],
            width=pdf_page_width * self.state.zoom_level,
            height=pdf_page_height * self.state.zoom_level,
        )
        
        # 创建容器 - 让容器自适应内容
        center_container = ft.Container(
            content=stack,
            expand=True,
            padding=5,  # 使用较小的padding
            alignment=ft.alignment.top_center,  # 图片在容器中水平居中
        )
        
        self.view.controls.append(center_container)
        
        # 更新页码显示
        self.state.page_counter.value = f"{page_index + 1}/{self.state.total_pages}"
        self.page.update()
        
        # 绘制当前页的bbox
        self.draw_bboxes(page_index)

    def _generate_page_image_unified(self, page_index, img_path):
        """🔧 统一的图片生成方法，与PDFProcessor保持一致"""
        try:
            page = self.state.pdf_doc.load_page(page_index)

            # 使用与pdf_processor.py中_generate_page_image完全相同的参数
            from utils.pdf_processor import PDF_IMAGE_SCALE
            pix = page.get_pixmap(
                matrix=fitz.Matrix(PDF_IMAGE_SCALE, PDF_IMAGE_SCALE),
                clip=page.mediabox,  # 关键！确保与预生成图片一致
                alpha=False
            )
            pix.save(img_path)

            # 返回页面信息，与PDFProcessor._generate_page_image保持一致
            page_info = {
                "original_width": page.rect.width,
                "original_height": page.rect.height,
                "rotation": page.rotation,
                "image_scale": PDF_IMAGE_SCALE,
                "image_width": pix.width,
                "image_height": pix.height
            }

            print(f"DEBUG: 统一生成图片 page_{page_index+1}.png，尺寸: {pix.width}x{pix.height}")
            return page_info

        except Exception as e:
            print(f"ERROR: 生成第{page_index+1}页图片失败: {e}")
            return None

    def _verify_image_consistency(self, page_index):
        """🔧 验证图片生成一致性的调试方法"""
        if not self.scale_info or "pages" not in self.scale_info:
            print(f"DEBUG: 页面 {page_index} 无缩放信息")
            return False

        if page_index not in self.scale_info["pages"]:
            print(f"DEBUG: 页面 {page_index} 缩放信息缺失")
            return False

        page_info = self.scale_info["pages"][page_index]
        img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_index)

        if os.path.exists(img_path):
            # 检查实际图片文件尺寸
            from PIL import Image
            try:
                with Image.open(img_path) as img:
                    actual_width, actual_height = img.size
                    expected_width = page_info.get("image_width", 0)
                    expected_height = page_info.get("image_height", 0)

                    if actual_width == expected_width and actual_height == expected_height:
                        print(f"DEBUG: 页面 {page_index} 图片尺寸一致: {actual_width}x{actual_height}")
                        return True
                    else:
                        print(f"DEBUG: 页面 {page_index} 图片尺寸不一致!")
                        print(f"  实际: {actual_width}x{actual_height}")
                        print(f"  预期: {expected_width}x{expected_height}")
                        return False
            except Exception as e:
                print(f"DEBUG: 无法检查图片尺寸: {e}")
                return False
        else:
            print(f"DEBUG: 图片文件不存在: {img_path}")
            return False

    def _calculate_image_display_size(self, image_width, image_height, container_width, container_height, fit_mode=ft.ImageFit.FIT_WIDTH):
        """🔧 计算图片在不同适应模式下的实际显示尺寸"""
        image_aspect_ratio = image_width / image_height
        container_aspect_ratio = container_width / container_height

        if fit_mode == ft.ImageFit.FIT_WIDTH:
            # FIT_WIDTH: 图片宽度适应容器宽度，高度按比例缩放
            actual_width = container_width
            actual_height = container_width / image_aspect_ratio

        elif fit_mode == ft.ImageFit.FIT_HEIGHT:
            # FIT_HEIGHT: 图片高度适应容器高度，宽度按比例缩放
            actual_height = container_height
            actual_width = container_height * image_aspect_ratio

        elif fit_mode == ft.ImageFit.CONTAIN:
            # CONTAIN: 图片完全包含在容器内，保持比例
            if image_aspect_ratio > container_aspect_ratio:
                # 图片更宽，按宽度适应
                actual_width = container_width
                actual_height = container_width / image_aspect_ratio
            else:
                # 图片更高，按高度适应
                actual_height = container_height
                actual_width = container_height * image_aspect_ratio

        elif fit_mode == ft.ImageFit.COVER:
            # COVER: 图片覆盖整个容器，可能被裁剪
            if image_aspect_ratio > container_aspect_ratio:
                # 图片更宽，按高度适应
                actual_height = container_height
                actual_width = container_height * image_aspect_ratio
            else:
                # 图片更高，按宽度适应
                actual_width = container_width
                actual_height = container_width / image_aspect_ratio

        else:
            # 默认情况，假设填充整个容器
            actual_width = container_width
            actual_height = container_height

        return actual_width, actual_height

    def _calculate_image_offset(self, actual_display_width, actual_display_height, container_width, container_height):
        """🔧 计算图片在容器中的偏移量（用于居中对齐）"""
        # 计算图片在容器中的偏移
        offset_x = (container_width - actual_display_width) / 2
        offset_y = (container_height - actual_display_height) / 2

        # 确保偏移不为负数
        offset_x = max(0, offset_x)
        offset_y = max(0, offset_y)

        return offset_x, offset_y

    def _get_actual_image_info(self, page_index, img_path, pdf_page_width, pdf_page_height):
        """🔧 获取实际图片信息，包含智能回退机制"""
        # 优先使用缓存的缩放信息
        if self.scale_info and "pages" in self.scale_info and page_index in self.scale_info["pages"]:
            page_info = self.scale_info["pages"][page_index]
            image_scale = page_info.get("image_scale", 2.0)
            image_width = page_info.get("image_width", pdf_page_width * image_scale)
            image_height = page_info.get("image_height", pdf_page_height * image_scale)
            print(f"DEBUG: 使用缓存的缩放信息 - scale={image_scale}, size={image_width}x{image_height}")
            return image_scale, image_width, image_height, "cached"

        # 尝试从实际图片文件获取尺寸
        if os.path.exists(img_path):
            try:
                from PIL import Image
                with Image.open(img_path) as img:
                    actual_width, actual_height = img.size
                    # 根据实际图片尺寸反推缩放因子
                    scale_x = actual_width / pdf_page_width
                    scale_y = actual_height / pdf_page_height

                    # 检查缩放因子是否一致（允许小误差）
                    if abs(scale_x - scale_y) < 0.01:
                        image_scale = scale_x
                        print(f"DEBUG: 从实际图片获取缩放信息 - scale={image_scale:.3f}, size={actual_width}x{actual_height}")

                        # 更新缓存
                        if not hasattr(self, 'scale_info') or not self.scale_info:
                            self.scale_info = {"pdf_image_scale": image_scale, "pages": {}}
                        if "pages" not in self.scale_info:
                            self.scale_info["pages"] = {}

                        self.scale_info["pages"][page_index] = {
                            "original_width": pdf_page_width,
                            "original_height": pdf_page_height,
                            "rotation": 0,  # 假设无旋转
                            "image_scale": image_scale,
                            "image_width": actual_width,
                            "image_height": actual_height
                        }

                        return image_scale, actual_width, actual_height, "file_analysis"
                    else:
                        print(f"DEBUG: ⚠️  图片缩放因子不一致 scale_x={scale_x:.3f}, scale_y={scale_y:.3f}")

            except Exception as e:
                print(f"DEBUG: 无法读取图片文件 {img_path}: {e}")

        # 最后回退到默认值
        from utils.pdf_processor import PDF_IMAGE_SCALE
        default_scale = PDF_IMAGE_SCALE
        default_width = pdf_page_width * default_scale
        default_height = pdf_page_height * default_scale
        print(f"DEBUG: 使用默认缩放信息 - scale={default_scale}, size={default_width}x{default_height}")

        return default_scale, default_width, default_height, "default"

    def _validate_and_repair_scale_info(self, page_index):
        """🔧 验证和修复缩放信息的完整性"""
        if not hasattr(self, 'scale_info') or not self.scale_info:
            print(f"DEBUG: 缩放信息完全缺失，需要重建")
            return False

        if "pages" not in self.scale_info:
            print(f"DEBUG: 缩放信息中缺少pages字段")
            self.scale_info["pages"] = {}
            return False

        if page_index not in self.scale_info["pages"]:
            print(f"DEBUG: 页面 {page_index} 的缩放信息缺失")
            return False

        page_info = self.scale_info["pages"][page_index]
        required_fields = ["image_scale", "image_width", "image_height", "original_width", "original_height"]
        missing_fields = [field for field in required_fields if field not in page_info]

        if missing_fields:
            print(f"DEBUG: 页面 {page_index} 缺少字段: {missing_fields}")
            return False

        # 验证数据的合理性
        image_scale = page_info.get("image_scale", 0)
        image_width = page_info.get("image_width", 0)
        image_height = page_info.get("image_height", 0)
        original_width = page_info.get("original_width", 0)
        original_height = page_info.get("original_height", 0)

        if image_scale <= 0 or image_width <= 0 or image_height <= 0:
            print(f"DEBUG: 页面 {page_index} 数据不合理: scale={image_scale}, size={image_width}x{image_height}")
            return False

        # 验证缩放关系的一致性
        expected_width = original_width * image_scale
        expected_height = original_height * image_scale

        if abs(image_width - expected_width) > 1 or abs(image_height - expected_height) > 1:
            print(f"DEBUG: 页面 {page_index} 缩放关系不一致:")
            print(f"  实际: {image_width}x{image_height}")
            print(f"  预期: {expected_width}x{expected_height}")
            return False

        print(f"DEBUG: 页面 {page_index} 缩放信息验证通过")
        return True

    def repair_missing_scale_info(self, force_repair=False):
        """🔧 批量修复缺失的缩放信息"""
        if not self.state.pdf_doc:
            print("DEBUG: 无PDF文档，无法修复缩放信息")
            return False

        total_pages = self.state.pdf_doc.page_count
        repaired_count = 0

        print(f"DEBUG: 开始检查 {total_pages} 页的缩放信息...")

        for page_idx in range(total_pages):
            needs_repair = force_repair or not self._validate_and_repair_scale_info(page_idx)

            if needs_repair:
                print(f"DEBUG: 修复页面 {page_idx} 的缩放信息...")

                # 获取页面信息
                page = self.state.pdf_doc.load_page(page_idx)
                pdf_page_width = page.rect.width
                pdf_page_height = page.rect.height

                # 获取图片路径
                img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_idx)

                # 使用智能回退获取图片信息
                image_scale, image_width, image_height, info_source = self._get_actual_image_info(
                    page_idx, img_path, pdf_page_width, pdf_page_height
                )

                print(f"DEBUG: 页面 {page_idx} 修复完成，信息来源: {info_source}")
                repaired_count += 1

        if repaired_count > 0:
            print(f"DEBUG: 成功修复 {repaired_count} 页的缩放信息")
            return True
        else:
            print(f"DEBUG: 所有页面的缩放信息都正常")
            return False

    def diagnose_scale_info_issue(self):
        """🔧 诊断缩放信息问题"""
        if not self.state.pdf_doc:
            print("DEBUG: 无PDF文档")
            return

        total_pages = self.state.pdf_doc.page_count
        print(f"DEBUG: 📊 缩放信息诊断报告 (总页数: {total_pages})")

        # 检查缩放信息文件
        if self.scale_info:
            pages_in_scale_info = len(self.scale_info.get("pages", {}))
            print(f"DEBUG: scale_info.json 包含 {pages_in_scale_info} 页信息")
            print(f"DEBUG: 页面键: {list(self.scale_info.get('pages', {}).keys())}")
        else:
            print("DEBUG: ❌ 没有缩放信息")

        # 检查每页的情况
        for page_idx in range(min(total_pages, 5)):  # 限制输出，避免过多信息
            print(f"\nDEBUG: 📄 页面 {page_idx} 诊断:")

            # 检查图片文件
            img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_idx)
            img_exists = os.path.exists(img_path)
            print(f"DEBUG:   图片文件: {'✅ 存在' if img_exists else '❌ 不存在'}")

            if img_exists:
                try:
                    from PIL import Image
                    with Image.open(img_path) as img:
                        actual_size = img.size
                        print(f"DEBUG:   图片尺寸: {actual_size[0]} x {actual_size[1]}")
                except Exception as e:
                    print(f"DEBUG:   图片读取错误: {e}")

            # 检查缩放信息
            has_scale_info = self._validate_and_repair_scale_info(page_idx)
            print(f"DEBUG:   缩放信息: {'✅ 有效' if has_scale_info else '❌ 缺失/无效'}")

        print(f"DEBUG: 📊 诊断完成")

    def draw_bboxes(self, page_index):
        """绘制当前页的bbox区域"""
        if not hasattr(self.state, 'json_data') or not self.state.json_data:
            return

        pdf_info = self.state.json_data.get('pdf_info', [])
        if page_index >= len(pdf_info):
            return

        page_obj = pdf_info[page_index]
        blocks = page_obj.get('para_blocks', [])

        if not self.view.controls or not isinstance(self.view.controls[0], ft.Container):
            return

        center_container = self.view.controls[0]
        if not center_container.content or not isinstance(center_container.content, ft.Stack):
            return

        stack = center_container.content
        if len(stack.controls) > 1:
            stack.controls = stack.controls[:1]  # 保留图片控件

        page = self.state.pdf_doc.load_page(page_index)
        pdf_page_width = page.rect.width
        pdf_page_height = page.rect.height

        # 🔧 验证图片生成一致性
        self._verify_image_consistency(page_index)

        # 🔧 验证和修复缩放信息
        scale_info_valid = self._validate_and_repair_scale_info(page_index)
        if not scale_info_valid:
            print(f"DEBUG: 页面 {page_index} 缩放信息无效，将使用智能回退")

        # 🔧 增强缩放信息回退：使用智能回退机制获取图片信息
        img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_index)
        image_scale, image_width, image_height, info_source = self._get_actual_image_info(
            page_index, img_path, pdf_page_width, pdf_page_height
        )

        print(f"DEBUG: 图片信息来源: {info_source}")
        print(f"DEBUG: 图片生成尺寸 {image_width}x{image_height} (scale={image_scale:.3f})")

        # 🔧 诊断：检查PDF页面的实际尺寸
        page = self.state.pdf_doc.load_page(page_index)
        actual_pdf_width = page.rect.width
        actual_pdf_height = page.rect.height
        print(f"DEBUG: 🔍 PDF页面实际尺寸: {actual_pdf_width} x {actual_pdf_height}")

        # 检查尺寸一致性
        expected_pdf_width = image_width / image_scale
        expected_pdf_height = image_height / image_scale
        print(f"DEBUG: 🔍 从图片反推PDF尺寸: {expected_pdf_width} x {expected_pdf_height}")

        if abs(actual_pdf_width - expected_pdf_width) > 1 or abs(actual_pdf_height - expected_pdf_height) > 1:
            print(f"DEBUG: ⚠️  PDF尺寸不一致！这可能是定位问题的根源")
            print(f"DEBUG: 差异: 宽度差{abs(actual_pdf_width - expected_pdf_width):.1f}, 高度差{abs(actual_pdf_height - expected_pdf_height):.1f}")
        else:
            print(f"DEBUG: ✅ PDF尺寸一致")

        # 🔧 修复：使用精确的显示尺寸计算方法
        container_width = stack.width or pdf_page_width * self.state.zoom_level
        container_height = stack.height or pdf_page_height * self.state.zoom_level

        print(f"DEBUG: 🔍 zoom_level: {self.state.zoom_level}")
        print(f"DEBUG: 🔍 stack.width: {stack.width}, stack.height: {stack.height}")
        print(f"DEBUG: 🔍 计算容器尺寸: {pdf_page_width} * {self.state.zoom_level} = {pdf_page_width * self.state.zoom_level}")

        # 计算图片在FIT_WIDTH模式下的实际显示尺寸
        actual_display_width, actual_display_height = self._calculate_image_display_size(
            image_width, image_height, container_width, container_height, ft.ImageFit.FIT_WIDTH
        )

        # 计算实际的显示缩放因子
        display_scale_x = actual_display_width / image_width
        display_scale_y = actual_display_height / image_height

        print(f"DEBUG: 容器尺寸 {container_width:.1f}x{container_height:.1f}")
        print(f"DEBUG: 图片生成尺寸 {image_width}x{image_height}")
        print(f"DEBUG: 实际显示尺寸 {actual_display_width:.1f}x{actual_display_height:.1f}")
        print(f"DEBUG: 显示缩放因子 x={display_scale_x:.3f}, y={display_scale_y:.3f}")

        # 计算图片在容器中的偏移（用于居中对齐）
        offset_x, offset_y = self._calculate_image_offset(
            actual_display_width, actual_display_height, container_width, container_height
        )

        # 检查显示尺寸是否超出容器（用于调试）
        if actual_display_height > container_height:
            print(f"DEBUG: ⚠️  图片显示高度 {actual_display_height:.1f} 超出容器高度 {container_height:.1f}")
        if actual_display_width > container_width:
            print(f"DEBUG: ⚠️  图片显示宽度 {actual_display_width:.1f} 超出容器宽度 {container_width:.1f}")

        if offset_x > 0 or offset_y > 0:
            print(f"DEBUG: 图片偏移 offset_x={offset_x:.1f}, offset_y={offset_y:.1f}")

        for block_idx, block in enumerate(blocks):
            bbox = block.get('bbox', [])
            if len(bbox) != 4:
                continue

            x0, y0, x1, y1 = bbox

            # ✅ 正确的坐标转换：PDF → 图片 → 显示
            left = x0 * image_scale * display_scale_x + offset_x
            top = y0 * image_scale * display_scale_y + offset_y
            width = (x1 - x0) * image_scale * display_scale_x
            height = (y1 - y0) * image_scale * display_scale_y

            print(f"DEBUG: 原始bbox={bbox}")
            print(f"DEBUG: 转换后 left={left:.1f}, top={top:.1f}, width={width:.1f}, height={height:.1f}")

            # 🔧 诊断：检查矩形框是否在合理范围内
            if left < 0 or top < 0:
                print(f"DEBUG: ⚠️  矩形框位置为负数！left={left:.1f}, top={top:.1f}")
            if left > container_width or top > container_height:
                print(f"DEBUG: ⚠️  矩形框超出容器范围！容器={container_width}x{container_height}")
            if width <= 0 or height <= 0:
                print(f"DEBUG: ⚠️  矩形框尺寸无效！width={width:.1f}, height={height:.1f}")

            print(f"DEBUG: 🎯 矩形框在容器中的相对位置: ({left/container_width*100:.1f}%, {top/container_height*100:.1f}%)")

            is_selected = (
                self.state.selected_page == page_index and
                self.state.selected_block == block_idx
            )
            border_color = ft.Colors.BLUE if is_selected else ft.Colors.RED

            bbox_container = ft.Container(
                left=left,
                top=top,
                width=width,
                height=height,
                border=ft.border.all(2, border_color),
                bgcolor=ft.Colors.TRANSPARENT,
                expand=False,
                on_click=lambda e, idx=block_idx: self.on_bbox_click(e, page_index, idx)
            )

            stack.controls.append(bbox_container)

        self.page.update()

    def on_bbox_click(self, e, page_index, block_idx):
        """处理bbox区域点击事件"""
        if self.processing_mode:
            return  # 处理中禁用点击
            
        # 使用状态setter方法触发监听器
        self.state.set_selected_page(page_index)
        self.state.set_selected_block(block_idx)
        if hasattr(self.state, 'status_manager') and self.state.status_manager:
            self.page.run_task(
                self.state.status_manager.set_user_action_status,
                f"已选择: 第{page_index + 1}页, 第{block_idx + 1}个内容块"
            )
        # 更新JSON编辑器选中状态
        if hasattr(self.state, 'json_editor') and self.state.json_editor:
            self.state.json_editor.highlight_selected_block()
        # 重新绘制以更新颜色
        self.draw_bboxes(page_index)

    def _set_processing_mode(self, processing: bool):
        """设置处理模式，禁用/启用导航和缩放按钮"""
        self.processing_mode = processing
        
        # 禁用/启用导航和缩放按钮
        if hasattr(self, 'prev_btn'):
            self.prev_btn.disabled = processing
        if hasattr(self, 'next_btn'):
            self.next_btn.disabled = processing
        if hasattr(self, 'zoom_out_btn'):
            self.zoom_out_btn.disabled = processing
        if hasattr(self, 'zoom_in_btn'):
            self.zoom_in_btn.disabled = processing
            
        self.page.update()

    def prev_page(self, e):
        if self.processing_mode:
            return  # 处理中禁用翻页
        if self.state.current_page_index > 0:
            self.state.current_page_index -= 1
            self.render_page(self.state.current_page_index)
    
    def zoom_out(self, e):
        if self.processing_mode:
            return  # 处理中禁用缩放
        if self.state.zoom_level > 1.0:  # 统一使用state.zoom_level
            self.state.zoom_level -= 0.5
            self.render_page(self.state.current_page_index)

    def zoom_in(self, e):
        if self.processing_mode:
            return  # 处理中禁用缩放
        if self.state.zoom_level < 5.0:  # 统一使用state.zoom_level
            self.state.zoom_level += 0.5
            self.render_page(self.state.current_page_index)

    def next_page(self, e):
        if self.processing_mode:
            return  # 处理中禁用翻页
        if self.state.current_page_index < self.state.total_pages - 1:
            self.state.current_page_index += 1
            self.render_page(self.state.current_page_index)