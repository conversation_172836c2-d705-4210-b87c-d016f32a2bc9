import asyncio
import fitz
import flet as ft
import os
from utils.pdf_processor import PDFProcessor

class PDFViewerComponent:
    def __init__(self, page, state):
        self.page = page
        self.state = state
        self.view = ft.Column(expand=True, spacing=0, scroll=ft.ScrollMode.AUTO)
        self.bbox_containers = []  # 存储当前页的bbox容器
        self.pdf_processor = PDFProcessor()
        self.processing_mode = False
        self.scale_info = None

    def create(self):
        # 注册UI控制回调
        self.state.add_ui_control_callback(self._set_processing_mode)
        
        # 创建导航按钮引用
        self.prev_btn = ft.IconButton(ft.Icons.NAVIGATE_BEFORE, on_click=self.prev_page)
        self.next_btn = ft.IconButton(ft.Icons.NAVIGATE_NEXT, on_click=self.next_page)
        self.zoom_out_btn = ft.IconButton(ft.Icons.ZOOM_OUT, on_click=self.zoom_out)
        self.zoom_in_btn = ft.IconButton(ft.Icons.ZOOM_IN, on_click=self.zoom_in)
        
        return ft.Container(
            content=ft.Column(
                expand=False,
                alignment=ft.MainAxisAlignment.START,
                horizontal_alignment=ft.CrossAxisAlignment.START,
                controls=[
                    ft.Row(
                        controls=[
                            self.prev_btn,
                            self.state.page_counter,
                            self.next_btn,
                            self.zoom_out_btn,
                            self.zoom_in_btn,
                        ],
                        alignment=ft.MainAxisAlignment.CENTER
                    ),
                    ft.Container(
                        content=self.view,
                        expand=True,
                        border=ft.border.all(1, ft.Colors.GREY_400),
                        padding=0,
                        border_radius=5,
                        margin=0
                    )
                ]
            ),
            expand=True,
            alignment=ft.alignment.top_left
        )
    
    async def load_pdf(self, pdf_path):
        # 强制重置所有相关状态
        self.state.current_pdf = pdf_path
        self.state.reset_all_selections()
        
        # 重新打开PDF文档
        self.state.pdf_doc = await asyncio.to_thread(fitz.open, pdf_path)
        self.state.total_pages = self.state.pdf_doc.page_count
        
        # 获取缩放信息
        self.scale_info = self.pdf_processor.get_scale_info(pdf_path)
        if self.scale_info:
            print(f"DEBUG: 加载缩放信息成功: {self.scale_info}")
        else:
            print("DEBUG: 警告 - 未找到缩放信息")
        
        # 强制清除视图控件和矩形框
        self.view.controls.clear()
        self.bbox_containers = []
        
        # 确保使用最新的JSON数据
        if hasattr(self.state, 'json_data') and self.state.json_data:
            self.render_page(0)
            self.draw_bboxes(0)
        else:
            # 如果没有JSON数据，只渲染页面
            self.render_page(0)

    def render_page(self, page_index):
        self.view.controls.clear()
        self.bbox_containers = []
        
        if not self.state.pdf_doc or page_index >= self.state.total_pages:
            return
        
        # 获取预生成的图片路径
        img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_index)
        
        if not os.path.exists(img_path):
            # 如果图片不存在，创建它
            page = self.state.pdf_doc.load_page(page_index)
            zoom = 2.0  # 使用高DPI
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)
            pix.save(img_path)
        
        # 获取PDF页面尺寸
        page = self.state.pdf_doc.load_page(page_index)
        page_width = page.rect.width
        page_height = page.rect.height
        
        # 创建图片控件 - 宽度适应容器，高度按比例
        image = ft.Image(
            src=img_path,
            key=f"img_{page_index}_{self.state.zoom_level}",
            fit=ft.ImageFit.FIT_WIDTH,  # 适应宽度，高度按比例调整
            gapless_playback=True
        )
        pdf_page_width = page.rect.width
        pdf_page_height = page.rect.height
        # 创建Stack布局 - 设置固定尺寸以确保定位稳定
        stack = ft.Stack(
            controls=[image],
            width=pdf_page_width * self.state.zoom_level,
            height=pdf_page_height * self.state.zoom_level,
        )
        
        # 创建容器 - 让容器自适应内容
        center_container = ft.Container(
            content=stack,
            expand=True,
            padding=5,  # 使用较小的padding
            alignment=ft.alignment.top_center,  # 图片在容器中水平居中
        )
        
        self.view.controls.append(center_container)
        
        # 更新页码显示
        self.state.page_counter.value = f"{page_index + 1}/{self.state.total_pages}"
        self.page.update()
        
        # 绘制当前页的bbox
        self.draw_bboxes(page_index)

    def draw_bboxes(self, page_index):
        """绘制当前页的bbox区域"""
        if not hasattr(self.state, 'json_data') or not self.state.json_data:
            return

        pdf_info = self.state.json_data.get('pdf_info', [])
        if page_index >= len(pdf_info):
            return

        page_obj = pdf_info[page_index]
        blocks = page_obj.get('para_blocks', [])

        if not self.view.controls or not isinstance(self.view.controls[0], ft.Container):
            return

        center_container = self.view.controls[0]
        if not center_container.content or not isinstance(center_container.content, ft.Stack):
            return

        stack = center_container.content
        if len(stack.controls) > 1:
            stack.controls = stack.controls[:1]  # 保留图片控件

        page = self.state.pdf_doc.load_page(page_index)
        pdf_page_width = page.rect.width
        pdf_page_height = page.rect.height

        # ✅ 获取图片实际尺寸（从scale_info.json）
        image_scale = 2.0  # 默认值
        image_width = pdf_page_width * image_scale
        image_height = pdf_page_height * image_scale

        if self.scale_info and str(page_index) in self.scale_info.get("pages", {}):
            page_info = self.scale_info["pages"][str(page_index)]
            image_scale = page_info.get("image_scale", 2.0)
            image_width = page_info.get("image_width", pdf_page_width * image_scale)
            image_height = page_info.get("image_height", pdf_page_height * image_scale)

        # ✅ 计算实际显示尺寸（根据ImageFit.FIT_WIDTH）
        container_width = stack.width or pdf_page_width * self.state.zoom_level
        container_height = stack.height or pdf_page_height * self.state.zoom_level

        # ✅ 计算从图片像素到显示像素的缩放因子
        display_scale_x = container_width / image_width
        display_scale_y = container_height / image_height

        print(f"DEBUG: 图片生成尺寸 {image_width}x{image_height}")
        print(f"DEBUG: 显示容器尺寸 {container_width}x{container_height}")
        print(f"DEBUG: 显示缩放因子 display_scale_x={display_scale_x:.3f}, display_scale_y={display_scale_y:.3f}")

        for block_idx, block in enumerate(blocks):
            bbox = block.get('bbox', [])
            if len(bbox) != 4:
                continue

            x0, y0, x1, y1 = bbox

            # ✅ 正确的坐标转换：PDF → 图片 → 显示
            left = x0 * image_scale * display_scale_x
            top = y0 * image_scale * display_scale_y
            width = (x1 - x0) * image_scale * display_scale_x
            height = (y1 - y0) * image_scale * display_scale_y

            print(f"DEBUG: 原始bbox={bbox}")
            print(f"DEBUG: 转换后 left={left:.1f}, top={top:.1f}, width={width:.1f}, height={height:.1f}")

            is_selected = (
                self.state.selected_page == page_index and
                self.state.selected_block == block_idx
            )
            border_color = ft.Colors.BLUE if is_selected else ft.Colors.RED

            bbox_container = ft.Container(
                left=left,
                top=top,
                width=width,
                height=height,
                border=ft.border.all(2, border_color),
                bgcolor=ft.Colors.TRANSPARENT,
                expand=False,
                on_click=lambda e, idx=block_idx: self.on_bbox_click(e, page_index, idx)
            )

            stack.controls.append(bbox_container)

        self.page.update()

    def on_bbox_click(self, e, page_index, block_idx):
        """处理bbox区域点击事件"""
        if self.processing_mode:
            return  # 处理中禁用点击
            
        # 使用状态setter方法触发监听器
        self.state.set_selected_page(page_index)
        self.state.set_selected_block(block_idx)
        if hasattr(self.state, 'status_manager') and self.state.status_manager:
            self.page.run_task(
                self.state.status_manager.set_user_action_status,
                f"已选择: 第{page_index + 1}页, 第{block_idx + 1}个内容块"
            )
        # 更新JSON编辑器选中状态
        if hasattr(self.state, 'json_editor') and self.state.json_editor:
            self.state.json_editor.highlight_selected_block()
        # 重新绘制以更新颜色
        self.draw_bboxes(page_index)

    def _set_processing_mode(self, processing: bool):
        """设置处理模式，禁用/启用导航和缩放按钮"""
        self.processing_mode = processing
        
        # 禁用/启用导航和缩放按钮
        if hasattr(self, 'prev_btn'):
            self.prev_btn.disabled = processing
        if hasattr(self, 'next_btn'):
            self.next_btn.disabled = processing
        if hasattr(self, 'zoom_out_btn'):
            self.zoom_out_btn.disabled = processing
        if hasattr(self, 'zoom_in_btn'):
            self.zoom_in_btn.disabled = processing
            
        self.page.update()

    def prev_page(self, e):
        if self.processing_mode:
            return  # 处理中禁用翻页
        if self.state.current_page_index > 0:
            self.state.current_page_index -= 1
            self.render_page(self.state.current_page_index)
    
    def zoom_out(self, e):
        if self.processing_mode:
            return  # 处理中禁用缩放
        if self.state.zoom_level > 1.0:  # 统一使用state.zoom_level
            self.state.zoom_level -= 0.5
            self.render_page(self.state.current_page_index)

    def zoom_in(self, e):
        if self.processing_mode:
            return  # 处理中禁用缩放
        if self.state.zoom_level < 5.0:  # 统一使用state.zoom_level
            self.state.zoom_level += 0.5
            self.render_page(self.state.current_page_index)

    def next_page(self, e):
        if self.processing_mode:
            return  # 处理中禁用翻页
        if self.state.current_page_index < self.state.total_pages - 1:
            self.state.current_page_index += 1
            self.render_page(self.state.current_page_index)