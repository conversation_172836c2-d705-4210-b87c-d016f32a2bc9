# MinerU Data Fix Tool

## 项目概述

MinerU Data Fix Tool 是一个专业的PDF文档数据修复和处理工具，基于现代化的Flet框架构建。该工具提供了直观的图形用户界面，用于可视化编辑和管理PDF文档的结构化数据，支持PDF查看、JSON数据编辑、内容增删改等多种功能。

## 核心功能

### 📄 PDF文档管理
- **多文档支持**: 支持批量导入和管理多个PDF文件
- **实时预览**: 提供高质量的PDF页面渲染和缩放功能
- **页面导航**: 支持前后翻页、页码显示和快速跳转

### 🎯 可视化编辑
- **同步显示**: PDF页面与JSON数据结构实时同步显示
- **区域高亮**: 支持在PDF页面上高亮显示对应的内容块
- **交互选择**: 点击PDF区域或JSON块实现双向选择同步

### 📝 JSON数据操作
- **结构化编辑**: 支持文本、表格、图像等多种内容类型的编辑
- **内容管理**: 提供添加、删除、修改内容块的完整操作
- **实时保存**: 自动保存修改后的JSON数据

### 🗂️ 文件系统管理
- **树形结构**: 提供文件夹和文件的树形视图
- **智能排序**: 支持自然排序，方便文档管理
- **快速加载**: 一键加载文件夹内的PDF和对应JSON文件

## 技术架构

### 前端技术栈
- **Flet框架**: 基于Flutter的现代Python GUI框架
- **响应式设计**: 自适应不同屏幕尺寸
- **现代化UI**: 采用Material Design设计语言

### 核心依赖
- **PyMuPDF (fitz)**: 高性能PDF处理库
- **Pillow**: 图像处理支持
- **asyncio**: 异步操作支持，提升用户体验

## 项目结构

```
mineru-data-fix-2/
├── main.py                 # 应用程序主入口
├── state.py               # 全局状态管理
├── README.md              # 项目文档
├── components/            # UI组件模块
│   ├── __init__.py
│   ├── file_tree.py      # 文件树组件
│   ├── json_editor.py    # JSON编辑器组件
│   └── pdf_viewer.py     # PDF查看器组件
├── operations/           # 数据操作模块
│   ├── __init__.py
│   ├── base_handler.py   # 基础处理器
│   ├── add_content.py    # 添加内容操作
│   ├── delete_content.py # 删除内容操作
│   ├── modify_content.py # 修改内容操作
│   ├── map_text_image.py # 文本图像映射
│   ├── page_image.py     # 页面图像处理
│   └── save_json.py      # JSON保存功能
├── utils/                # 工具函数
│   ├── __init__.py
│   └── dialogs.py        # 对话框工具
├── storage/              # 数据存储
│   ├── data/            # 数据文件目录
│   └── temp/            # 临时文件目录
│       └── pdf_pages/   # PDF页面缓存
└── .gitignore           # Git忽略文件
```

## 安装与运行

### 环境要求
- Python 3.8 或更高版本
- Windows/Linux/macOS 操作系统

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd mineru-data-fix-2
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行应用**
   ```bash
   python main.py
   ```

### 依赖列表
```txt
flet>=0.10.0
PyMuPDF>=1.23.0
Pillow>=9.0.0
```

## 使用指南

### 快速开始

1. **启动应用**: 运行 `python main.py` 启动应用程序
2. **导入数据**: 点击"导入文件夹"按钮选择包含PDF文件的文件夹
3. **查看文档**: 在左侧文件树中选择要编辑的PDF文件
4. **编辑数据**: 在右侧JSON编辑器中查看和编辑文档结构
5. **保存修改**: 所有修改会自动保存到对应的JSON文件中

### 操作流程

#### 1. 文档加载
- 点击"导入文件夹"选择包含PDF和layout.json的文件夹
- 系统自动加载PDF文件和对应的JSON数据
- 如果layout.json不存在，系统会创建空的数据结构

#### 2. 内容编辑
- **选择内容**: 点击PDF页面上的高亮区域或JSON编辑器中的内容块
- **添加内容**: 使用"在前方新增内容"或"在后方新增内容"按钮
- **修改内容**: 直接在JSON编辑器中修改文本内容
- **删除内容**: 使用删除功能移除不需要的内容块

#### 3. 数据保存
- 所有修改会自动保存到layout.json文件中
- 支持随时撤销和重做操作

## 功能详解

### 文件树组件 (file_tree.py)
- 树形展示文件夹结构
- 支持自然排序显示文件名
- 实时加载选中的PDF和JSON文件

### PDF查看器 (pdf_viewer.py)
- 高质量PDF页面渲染
- 支持缩放、翻页等操作
- 在PDF页面上显示内容块边界
- 支持点击选择内容块

### JSON编辑器 (json_editor.py)
- 结构化显示JSON数据
- 支持文本、表格、图像等多种内容类型
- 实时高亮显示当前选中的内容块
- 支持内容搜索和定位

### 数据操作模块
- **添加内容**: 在指定位置添加新的文本或图像内容
- **删除内容**: 安全删除不需要的内容块
- **修改内容**: 编辑现有内容的文本和属性
- **保存功能**: 自动保存所有修改到JSON文件

## 开发指南

### 项目扩展
项目采用模块化设计，易于扩展新功能：

1. **添加新组件**: 在`components/`目录下创建新的UI组件
2. **扩展操作**: 在`operations/`目录下添加新的数据处理功能
3. **自定义工具**: 在`utils/`目录下添加通用工具函数

### 状态管理
- 使用集中式状态管理（state.py）
- 支持状态监听和自动UI更新
- 提供线程安全的状态修改方法

### 错误处理
- 完善的异常捕获和错误提示
- 用户友好的错误信息显示
- 自动恢复机制确保数据安全

## 贡献指南

### 开发规范
1. 遵循PEP 8代码规范
2. 添加必要的注释和文档字符串
3. 为新功能编写测试用例
4. 保持向后兼容性

### 提交规范
- 使用清晰的提交信息
- 每次提交专注于单一功能
- 包含适当的测试和文档更新

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 支持与联系

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目GitHub仓库
- 发送邮件至项目维护者

## 更新日志

### v1.1.0 (新增功能)
- ✅ **PDF图片预处理**: 自动预生成所有PDF页面图片，提升加载速度
- ✅ **进度条显示**: 图片处理过程显示实时进度条
- ✅ **智能检查**: 自动检测已存在的图片，避免重复处理
- ✅ **永久存储**: 图片保存在PDF所在文件夹的images目录下
- ✅ **性能优化**: 使用并发处理加快图片生成速度

### v1.0.0 (基础版本)
- ✅ 基础PDF查看功能
- ✅ JSON数据可视化编辑
- ✅ 文件树管理
- ✅ 内容块操作（添加、删除、修改）
- ✅ 自动保存功能
- ✅ 响应式UI设计

### 未来计划
- 🔄 支持更多PDF内容类型
- 🔄 批量处理功能
- 🔄 数据验证和清理
- 🔄 导出多种格式支持
- 🔄 插件系统支持