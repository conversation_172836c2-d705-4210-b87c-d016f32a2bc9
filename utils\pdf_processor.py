import os
import fitz
import asyncio
import flet as ft
import threading
import json
from utils.status_manager import StatusManager

# 全局常量：PDF图片预生成时的固定缩放比例
PDF_IMAGE_SCALE = 2.0

class PDFProcessor:
    def __init__(self):
        self.cancel_event = threading.Event()
        self.status_manager = None
        
    async def process_pdf_images(self, pdf_path, status_manager, on_complete=None):
        """处理PDF图片预生成，带进度条"""
        if not os.path.exists(pdf_path):
            return False
            
        # 获取PDF文件夹路径
        pdf_dir = os.path.dirname(pdf_path)
        images_dir = os.path.join(pdf_dir, "images")
        
        # 确保images目录存在
        if not os.path.exists(images_dir):
            os.makedirs(images_dir)
            
        # 打开PDF文件
        try:
            doc = fitz.open(pdf_path)
            total_pages = doc.page_count
            
            # 检查哪些页面需要处理
            pages_to_process = []
            for i in range(total_pages):
                img_path = os.path.join(images_dir, f"page_{i+1}.png")
                if not os.path.exists(img_path):
                    pages_to_process.append(i)
            
            if not pages_to_process:
                doc.close()
                if on_complete:
                    await on_complete()
                return True
                
            # 显示进度条
            print(f"DEBUG: 需要处理 {len(pages_to_process)} 页，总页面 {total_pages}")
            
            # 直接使用传入的状态管理器
            if status_manager:
                self.status_manager = status_manager
                await self.status_manager.set_processing_status(
                    f"正在处理PDF图片... 0/{len(pages_to_process)}", 0
                )
                print("DEBUG: 状态栏已设置为初始状态")
            else:
                print("DEBUG: 警告 - 状态管理器未提供，状态栏不会更新")
            
            # 使用asyncio.to_thread实现非阻塞处理
            completed = 0
            total_to_process = len(pages_to_process)
            
            # 记录缩放信息
            scale_info = {
                "pdf_image_scale": PDF_IMAGE_SCALE,
                "pages": {}
            }
            
            for page_idx in pages_to_process:
                if self.cancel_event.is_set():
                    break
                try:
                    # 非阻塞执行图片生成，并获取页面信息
                    page_info = await asyncio.to_thread(self._generate_page_image, doc, page_idx, images_dir)
                    if page_info:
                        # 🔧 修复：统一使用整数键格式，确保与pdf_viewer.py中的查找一致
                        scale_info["pages"][page_idx] = page_info
                        print(f"DEBUG: 保存页面 {page_idx} 的缩放信息: {page_info}")
                    completed += 1
                    
                    # 更新进度并给UI更新机会
                    await self._update_progress(completed, total_to_process)
                    await asyncio.sleep(0)  # 让出控制权给UI
                    
                except Exception as e:
                    print(f"处理第{page_idx+1}页失败: {e}")
            
            # 保存缩放信息
            scale_info_path = os.path.join(images_dir, "scale_info.json")

            # 🔧 修复：确保页面键为整数格式，避免JSON序列化时的类型转换问题
            # 重新整理pages数据，确保键为整数
            if "pages" in scale_info:
                pages_data = {}
                for key, value in scale_info["pages"].items():
                    # 确保键为整数
                    int_key = int(key) if isinstance(key, str) else key
                    pages_data[int_key] = value
                scale_info["pages"] = pages_data
                print(f"DEBUG: 整理后的页面键: {list(pages_data.keys())}")

            with open(scale_info_path, 'w', encoding='utf-8') as f:
                json.dump(scale_info, f, indent=2, ensure_ascii=False)
            
            doc.close()
            await self._close_progress_dialog()
            
            if not self.cancel_event.is_set() and on_complete:
                await on_complete()
                
            return not self.cancel_event.is_set()
            
        except Exception as e:
            print(f"处理PDF图片失败: {e}")
            import traceback
            traceback.print_exc()
            await self._close_progress_dialog()
            return False
    
    def _generate_page_image(self, doc, page_idx, images_dir):
        """生成单页图片，返回页面信息 (已修正)"""
        try:
            page = doc.load_page(page_idx)
            
            # --- 核心修正：强制渲染整个 MediaBox ---
            # 这可以确保生成的图片内容与 page.rect 的尺寸完全对应，
            # 从而消除 CropBox 带来的坐标偏移问题。
            pix = page.get_pixmap(
                matrix=fitz.Matrix(PDF_IMAGE_SCALE, PDF_IMAGE_SCALE),
                clip=page.mediabox,  # 关键！
                alpha=False
            )
            
            img_path = os.path.join(images_dir, f"page_{page_idx+1}.png")
            pix.save(img_path)
            
            # 返回页面信息，现在可以确信 image_width/height 与 page.rect 成比例
            return {
                "original_width": page.rect.width,
                "original_height": page.rect.height,
                "rotation": page.rotation, # 同时记录旋转角度
                "image_scale": PDF_IMAGE_SCALE,
                "image_width": pix.width,
                "image_height": pix.height
            }
        except Exception as e:
            print(f"生成第{page_idx+1}页图片失败: {e}")
            return None
    
    def get_scale_info(self, pdf_path):
        """获取PDF的缩放信息"""
        pdf_dir = os.path.dirname(pdf_path)
        images_dir = os.path.join(pdf_dir, "images")
        scale_info_path = os.path.join(images_dir, "scale_info.json")

        if os.path.exists(scale_info_path):
            try:
                with open(scale_info_path, 'r', encoding='utf-8') as f:
                    scale_info = json.load(f)

                # 🔧 修复：确保页面键为整数格式，处理JSON反序列化后的字符串键问题
                if "pages" in scale_info and isinstance(scale_info["pages"], dict):
                    pages_data = {}
                    for key, value in scale_info["pages"].items():
                        # JSON反序列化后键可能变成字符串，转换回整数
                        try:
                            int_key = int(key)
                            pages_data[int_key] = value
                        except (ValueError, TypeError):
                            # 如果转换失败，保持原键
                            pages_data[key] = value
                    scale_info["pages"] = pages_data
                    print(f"DEBUG: 读取缩放信息，页面键: {list(pages_data.keys())}")

                return scale_info
            except Exception as e:
                print(f"读取缩放信息失败: {e}")

        return None
    
    async def _update_progress(self, completed, total):
        """更新进度"""
        if self.status_manager:
            progress_percent = int((completed / total) * 100)
            print(f"DEBUG: 更新进度 {completed}/{total} ({progress_percent}%)")
            await self.status_manager.update_processing_progress(completed, total)
        else:
            print(f"DEBUG: 警告 - 状态管理器未初始化，无法更新进度 {completed}/{total}")
    
    async def _close_progress_dialog(self):
        """隐藏进度条"""
        print("DEBUG: 隐藏进度条")
        if self.status_manager:
            await self.status_manager.complete_processing()
            print("DEBUG: 进度条已隐藏")
        else:
            print("DEBUG: 警告 - 状态管理器未初始化，无法隐藏进度条")
    
    def _cancel_processing(self, e):
        """取消处理"""
        self.cancel_event.set()
        # 取消按钮不再需要，因为进度条在toolbar中
    
    def get_page_image_path(self, pdf_path, page_index):
        """获取页面图片路径"""
        pdf_dir = os.path.dirname(pdf_path)
        images_dir = os.path.join(pdf_dir, "images")
        return os.path.join(images_dir, f"page_{page_index+1}.png")
    
    def check_images_exist(self, pdf_path):
        """检查所有页面图片是否存在"""
        if not os.path.exists(pdf_path):
            return False
            
        try:
            doc = fitz.open(pdf_path)
            total_pages = doc.page_count
            doc.close()
            
            pdf_dir = os.path.dirname(pdf_path)
            images_dir = os.path.join(pdf_dir, "images")
            
            for i in range(total_pages):
                img_path = os.path.join(images_dir, f"page_{i+1}.png")
                if not os.path.exists(img_path):
                    return False
            
            return True
        except:
            return False