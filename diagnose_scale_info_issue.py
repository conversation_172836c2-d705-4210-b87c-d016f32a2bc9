#!/usr/bin/env python3
"""
诊断缩放信息问题的脚本
分析为什么只有部分页面有缩放信息
"""

import os
import json
import sys

def diagnose_pdf_scale_info(pdf_path):
    """诊断PDF的缩放信息问题"""
    print(f"🔍 诊断PDF缩放信息: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return
    
    # 检查PDF基本信息
    try:
        import fitz
        doc = fitz.open(pdf_path)
        total_pages = doc.page_count
        print(f"📄 PDF总页数: {total_pages}")
        doc.close()
    except Exception as e:
        print(f"❌ 无法读取PDF: {e}")
        return
    
    # 检查images目录
    pdf_dir = os.path.dirname(pdf_path)
    images_dir = os.path.join(pdf_dir, "images")
    
    print(f"\n📁 检查images目录: {images_dir}")
    if not os.path.exists(images_dir):
        print("❌ images目录不存在")
        return
    
    # 检查图片文件
    print(f"\n🖼️  检查图片文件:")
    image_files = []
    for i in range(total_pages):
        img_path = os.path.join(images_dir, f"page_{i+1}.png")
        exists = os.path.exists(img_path)
        print(f"  页面 {i}: {'✅' if exists else '❌'} {img_path}")
        if exists:
            image_files.append((i, img_path))
            
            # 检查图片尺寸
            try:
                from PIL import Image
                with Image.open(img_path) as img:
                    size = img.size
                    print(f"    尺寸: {size[0]} x {size[1]}")
            except Exception as e:
                print(f"    ❌ 无法读取图片: {e}")
    
    # 检查scale_info.json
    scale_info_path = os.path.join(images_dir, "scale_info.json")
    print(f"\n📊 检查缩放信息文件: {scale_info_path}")
    
    if not os.path.exists(scale_info_path):
        print("❌ scale_info.json文件不存在")
        return
    
    try:
        with open(scale_info_path, 'r', encoding='utf-8') as f:
            scale_info = json.load(f)
        
        print("✅ scale_info.json文件存在")
        print(f"📊 全局缩放: {scale_info.get('pdf_image_scale', 'N/A')}")
        
        pages_info = scale_info.get("pages", {})
        print(f"📊 包含页面信息: {len(pages_info)} 页")
        print(f"📊 页面键: {list(pages_info.keys())}")
        print(f"📊 页面键类型: {[type(k).__name__ for k in pages_info.keys()]}")
        
        # 详细检查每页信息
        print(f"\n📄 详细页面信息:")
        for page_key, page_info in pages_info.items():
            print(f"  页面 {page_key}:")
            for field, value in page_info.items():
                print(f"    {field}: {value}")
        
        # 分析缺失的页面
        missing_pages = []
        for i in range(total_pages):
            if i not in pages_info and str(i) not in pages_info:
                missing_pages.append(i)
        
        if missing_pages:
            print(f"\n⚠️  缺失缩放信息的页面: {missing_pages}")
            
            # 检查这些页面是否有图片文件
            for page_idx in missing_pages:
                img_path = os.path.join(images_dir, f"page_{page_idx+1}.png")
                if os.path.exists(img_path):
                    print(f"  页面 {page_idx}: 有图片文件但缺少缩放信息")
                else:
                    print(f"  页面 {page_idx}: 图片文件和缩放信息都缺失")
        else:
            print("✅ 所有页面都有缩放信息")
            
    except Exception as e:
        print(f"❌ 无法读取scale_info.json: {e}")

def suggest_solutions():
    """建议解决方案"""
    print(f"\n💡 建议的解决方案:")
    print(f"1. 🔄 重新生成所有图片:")
    print(f"   - 删除整个images文件夹")
    print(f"   - 重新运行程序，让系统重新生成所有页面")
    
    print(f"\n2. 🔧 手动修复缺失信息:")
    print(f"   - 使用程序的智能回退机制")
    print(f"   - 从现有图片文件反推缩放信息")
    
    print(f"\n3. 📊 检查生成过程:")
    print(f"   - 查看是否有错误日志")
    print(f"   - 确认图片生成过程是否被中断")
    
    print(f"\n4. 🧪 使用诊断功能:")
    print(f"   - 程序已添加自动诊断和修复功能")
    print(f"   - 查看DEBUG输出了解详细情况")

def main():
    if len(sys.argv) < 2:
        print("用法: python diagnose_scale_info_issue.py <PDF文件路径>")
        print("示例: python diagnose_scale_info_issue.py /path/to/your.pdf")
        return
    
    pdf_path = sys.argv[1]
    diagnose_pdf_scale_info(pdf_path)
    suggest_solutions()

if __name__ == "__main__":
    main()
