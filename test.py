import fitz  # PyMuPDF
from PIL import Image

def get_pdf_page_size(pdf_path, page_num=0):
    doc = fitz.open(pdf_path)
    page = doc[page_num]
    width, height = page.rect.width, page.rect.height
    doc.close()
    return width, height

def get_image_size(image_path):
    with Image.open(image_path) as img:
        return img.width, img.height

def pdf_bbox_to_image_bbox(bbox, pdf_size, img_size):
    # PDF: (0,0)左下角，图片(0,0)左上角
    pdf_w, pdf_h = pdf_size
    img_w, img_h = img_size
    scale_x = img_w / pdf_w
    scale_y = img_h / pdf_h

    x0, y0, x1, y1 = bbox
    # y轴翻转
    y0_img = pdf_h - y0
    y1_img = pdf_h - y1

    # 转为像素
    x0_img = x0 * scale_x
    x1_img = x1 * scale_x
    y0_img = y0_img * scale_y
    y1_img = y1_img * scale_y

    # 左上角为原点
    left = min(x0_img, x1_img)
    right = max(x0_img, x1_img)
    top = min(y0_img, y1_img)
    bottom = max(y0_img, y1_img)
    return [left, top, right, bottom]

def check_bbox(pdf_path, image_path, bbox_list, page_num=0):
    pdf_size = get_pdf_page_size(pdf_path, page_num)
    img_size = get_image_size(image_path)
    print(f"PDF size (pt): {pdf_size}")
    print(f"Image size (px): {img_size}")

    for i, bbox in enumerate(bbox_list):
        img_bbox = pdf_bbox_to_image_bbox(bbox, pdf_size, img_size)
        print(f"\nBBox {i+1}: {bbox}")
        print(f"  -> Image bbox: {img_bbox}")
        # 检查是否超出图片范围
        if (img_bbox[0] < 0 or img_bbox[1] < 0 or
            img_bbox[2] > img_size[0] or img_bbox[3] > img_size[1]):
            print("  [!] 超出图片范围")
        else:
            print("  [OK] 在图片范围内")

if __name__ == "__main__":
    # 示例1：异常文件
    pdf_path = r"F:\work\yaozai\MinerU\训练材料已转换\02 全国民用建筑工程设计技术措施 防空地下室.pdf-7a6bf9e7-1607-45b9-a4f5-7c9e77d3033d\e0efe5dc-7713-4c76-8df5-6eb1488dab20_origin.pdf"
    image_path = r"F:\work\yaozai\MinerU\训练材料已转换\02 全国民用建筑工程设计技术措施 防空地下室.pdf-7a6bf9e7-1607-45b9-a4f5-7c9e77d3033d\images\page_1.png"
    bbox_list = [
        [177, 355, 1067, 673],
        [550, 1469, 1067, 1600]
    ]
    check_bbox(pdf_path, image_path, bbox_list, page_num=0)

    # 示例2：正常文件
    pdf_path2 = r"F:\work\yaozai\MinerU\训练材料已转换\1第一分册\1第一分册_origin.pdf"
    image_path2 = r"F:\work\yaozai\MinerU\训练材料已转换\1第一分册\images\page_1.png"
    bbox_list2 = [
        [107, 110, 416, 150],
        [224, 169, 298, 188],
        [246, 218, 265, 253],
        [153, 297, 366, 315],
        [202, 520, 217, 534]
    ]
    check_bbox(pdf_path2, image_path2, bbox_list2, page_num=0)