# 显示尺寸计算修复报告

## 🔍 问题分析

### 根本原因
在 `pdf_viewer.py` 的 `draw_bboxes` 方法中，原有的显示尺寸计算假设图片完全填充容器，但实际上 `ImageFit.FIT_WIDTH` 的行为更复杂：

1. **原有假设**（错误）：
   ```python
   # 假设图片完全填充容器
   display_scale_x = container_width / image_width
   display_scale_y = container_height / image_height
   ```

2. **实际行为**（正确）：
   - 图片宽度适应容器宽度
   - 图片高度按比例缩放，可能小于或大于容器高度
   - 当图片高度小于容器时，图片在容器中垂直居中，产生偏移

### 影响范围
- **标准比例PDF**：图片与容器比例相同时，影响较小
- **非标准比例PDF**：图片与容器比例不同时，矩形框位置偏差明显
- **不同缩放级别**：缩放时偏差会被放大

## 🔧 修复方案

### 1. 创建精确的显示尺寸计算方法

#### 新增方法：`_calculate_image_display_size`
```python
def _calculate_image_display_size(self, image_width, image_height, container_width, container_height, fit_mode=ft.ImageFit.FIT_WIDTH):
    """🔧 计算图片在不同适应模式下的实际显示尺寸"""
    image_aspect_ratio = image_width / image_height
    container_aspect_ratio = container_width / container_height
    
    if fit_mode == ft.ImageFit.FIT_WIDTH:
        # FIT_WIDTH: 图片宽度适应容器宽度，高度按比例缩放
        actual_width = container_width
        actual_height = container_width / image_aspect_ratio
        
    elif fit_mode == ft.ImageFit.FIT_HEIGHT:
        # FIT_HEIGHT: 图片高度适应容器高度，宽度按比例缩放
        actual_height = container_height
        actual_width = container_height * image_aspect_ratio
        
    elif fit_mode == ft.ImageFit.CONTAIN:
        # CONTAIN: 图片完全包含在容器内，保持比例
        if image_aspect_ratio > container_aspect_ratio:
            actual_width = container_width
            actual_height = container_width / image_aspect_ratio
        else:
            actual_height = container_height
            actual_width = container_height * image_aspect_ratio
            
    # ... 其他模式
    
    return actual_width, actual_height
```

### 2. 添加图片偏移计算

#### 新增方法：`_calculate_image_offset`
```python
def _calculate_image_offset(self, actual_display_width, actual_display_height, container_width, container_height):
    """🔧 计算图片在容器中的偏移量（用于居中对齐）"""
    # 计算图片在容器中的偏移
    offset_x = (container_width - actual_display_width) / 2
    offset_y = (container_height - actual_display_height) / 2
    
    # 确保偏移不为负数
    offset_x = max(0, offset_x)
    offset_y = max(0, offset_y)
    
    return offset_x, offset_y
```

### 3. 更新坐标转换逻辑

#### 修复前：
```python
# 简单假设图片填充整个容器
display_scale_x = container_width / image_width
display_scale_y = container_height / image_height

left = x0 * image_scale * display_scale_x
top = y0 * image_scale * display_scale_y
```

#### 修复后：
```python
# 计算图片在FIT_WIDTH模式下的实际显示尺寸
actual_display_width, actual_display_height = self._calculate_image_display_size(
    image_width, image_height, container_width, container_height, ft.ImageFit.FIT_WIDTH
)

# 计算实际的显示缩放因子
display_scale_x = actual_display_width / image_width
display_scale_y = actual_display_height / image_height

# 计算图片在容器中的偏移
offset_x, offset_y = self._calculate_image_offset(
    actual_display_width, actual_display_height, container_width, container_height
)

# 正确的坐标转换，包含图片偏移
left = x0 * image_scale * display_scale_x + offset_x
top = y0 * image_scale * display_scale_y + offset_y
```

## 🧪 验证测试

### 测试结果分析

#### 标准A4页面（比例相同）
```
📊 测试案例: 标准A4页面 (图片与容器比例相同)
   图片尺寸: 1190 x 1684
   容器尺寸: 595 x 842
   FIT_WIDTH显示尺寸: 595.0 x 842.0
   偏移量: x=0.0, y=0.0
   ✅ 显示尺寸在容器范围内
```

#### 宽图片（产生垂直偏移）
```
📊 测试案例: 宽图片 (图片比容器更宽)
   图片尺寸: 2000 x 1000
   容器尺寸: 600 x 800
   FIT_WIDTH显示尺寸: 600.0 x 300.0
   偏移量: x=0.0, y=250.0  ← 重要！垂直偏移
   ✅ 显示尺寸在容器范围内
```

#### 高图片（超出容器）
```
📊 测试案例: 高图片 (图片比容器更高)
   图片尺寸: 800 x 2000
   容器尺寸: 600 x 800
   FIT_WIDTH显示尺寸: 600.0 x 1500.0
   ⚠️  显示高度超出容器 700.0 像素
```

### 关键发现
1. **FIT_WIDTH模式**：图片宽度总是等于容器宽度
2. **垂直偏移**：当图片高度小于容器时，会产生垂直居中偏移
3. **超出处理**：当图片高度大于容器时，图片会被裁剪
4. **缩放因子**：X和Y方向的缩放因子相同（保持比例）

## 📊 修复效果

### 修复前
- 🔴 假设图片完全填充容器
- 🔴 忽略图片的实际显示尺寸
- 🔴 不考虑图片在容器中的偏移
- 🔴 不同比例的PDF矩形框位置偏差

### 修复后
- ✅ 精确计算图片的实际显示尺寸
- ✅ 考虑不同ImageFit模式的行为
- ✅ 正确处理图片在容器中的偏移
- ✅ 提供详细的调试信息

## 🎯 技术要点

### ImageFit.FIT_WIDTH的行为
1. **宽度适应**：图片宽度总是等于容器宽度
2. **高度按比例**：图片高度 = 容器宽度 / 图片宽高比
3. **垂直居中**：当图片高度 < 容器高度时，图片垂直居中
4. **可能裁剪**：当图片高度 > 容器高度时，图片被裁剪

### 坐标转换公式
```
实际显示宽度 = 容器宽度
实际显示高度 = 容器宽度 / 图片宽高比
显示缩放因子X = 实际显示宽度 / 图片宽度
显示缩放因子Y = 实际显示高度 / 图片高度
偏移X = max(0, (容器宽度 - 实际显示宽度) / 2)
偏移Y = max(0, (容器高度 - 实际显示高度) / 2)

矩形框X = PDF_X * 图片缩放 * 显示缩放X + 偏移X
矩形框Y = PDF_Y * 图片缩放 * 显示缩放Y + 偏移Y
```

## 🔮 预期改进

修复此问题后，应该能解决：
1. **不同比例PDF**的矩形框定位偏差
2. **缩放时**的矩形框位置不准确
3. **图片居中显示**时的坐标偏移问题

结合之前的修复（页面索引格式、统一图片生成），这三个修复应该能全面解决矩形框定位不一致的问题。

## 📝 使用建议

1. **测试不同比例的PDF**：验证修复在各种页面比例下的效果
2. **观察调试输出**：注意偏移量和显示尺寸的计算结果
3. **检查边缘情况**：特别关注极宽或极高的PDF页面
4. **验证缩放功能**：确保在不同缩放级别下矩形框位置正确

## 🚨 注意事项

1. **性能影响**：增加了显示尺寸计算，但计算量很小
2. **兼容性**：修复保持了与现有代码的兼容性
3. **调试信息**：增加了详细的调试输出，便于问题诊断
4. **扩展性**：支持其他ImageFit模式的扩展
