import os
import json
import fitz
import flet as ft
import atexit
from state import AppState
from components import FileTreeComponent, PDFViewerComponent, JSONEditorComponent
from operations import AddContentHandler
from utils import DialogManager
from utils.status_manager import StatusManager

class PDFEditorApp:
    def __init__(self, page):
        self.page = page
        self.state = AppState()
        self.state.status_text = ft.Text("就绪", size=14, color=ft.Colors.BLUE)
        self.state.page_counter = ft.Text("0/0", size=14, weight=ft.FontWeight.BOLD)
        self.state.progress_bar = ft.ProgressBar(width=200, visible=False)
        self.state.status_manager = StatusManager(self.state.status_text, self.state.progress_bar, self.page)
        
        # 初始化组件
        self.file_tree = FileTreeComponent(page, self.state)
        self.pdf_viewer = PDFViewerComponent(page, self.state)
        self.state.pdf_viewer = self.pdf_viewer
        self.json_editor = JSONEditorComponent(page, self.state)
        self.state.json_editor = self.json_editor
        
        # 初始化操作处理器
        self.operations = {
            'add_content': AddContentHandler(page, self.state, DialogManager)
        }
        
        # 注册UI控制回调
        self.state.add_ui_control_callback(self._set_ui_enabled)
        
        # 绑定页面方法

        self.page._close_dialog = self._close_dialog
        
        self._build_ui()
        self.page.window_maximized = True
        self.page.update()
        
    def _build_ui(self):
        # 创建按钮引用
        self.import_folder_btn = ft.ElevatedButton("导入文件夹", on_click=self._import_folder)
        self.refresh_btn = ft.ElevatedButton("刷新", on_click=self.file_tree.refresh)
        
        # 顶部工具栏
        toolbar = ft.Row(
            controls=[
                self.import_folder_btn,
                self.refresh_btn,
                ft.Text("状态:", size=14),
                self.state.status_text,
                self.state.progress_bar,
            ],
            alignment=ft.MainAxisAlignment.START,
            spacing=10
        )
        
        # 主内容区域
        main_content = ft.Row(
            expand=True,
            controls=[
                self.file_tree.create(),
                self.pdf_viewer.create(),
                self.json_editor.create(),
            ],
            spacing=10,
            vertical_alignment=ft.CrossAxisAlignment.STRETCH
        )
        
        # 底部操作按钮
        self.add_before_btn = ft.ElevatedButton("在前方新增内容", on_click=self.operations['add_content'].handle_before)
        self.add_after_btn = ft.ElevatedButton("在后方新增内容", on_click=self._add_content_after)
        
        bottom_buttons = ft.Row(
            controls=[
                self.add_before_btn,
                self.add_after_btn,
                # 其他按钮...
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO
        )
        
        # 组装页面
        self.page.add(
            ft.Column(
                expand=True,
                controls=[
                    toolbar,
                    ft.Divider(height=10),
                    main_content,
                    ft.Divider(height=10),
                    bottom_buttons
                ]
            )
        )
        
    async def _import_folder(self, e):
        folder_dialog = ft.FilePicker(on_result=lambda result: self.page.run_task(
            self._handle_folder_selected, result
        ))
        self.page.overlay.append(folder_dialog)
        self.page.update()
        folder_dialog.get_directory_path()
        
    async def _handle_folder_selected(self, result):
        if result.path:
            self.state.root_folder = result.path
            await self.file_tree.refresh(e=None)
  
            
    def _close_dialog(self, e, result):
        self.page._dialog_result = result
        self.page.dialog.open = False
        self.page.update()
        
    async def _add_content_after(self, e):
        # 实现"在后方新增"逻辑
        pass
    
    def _set_ui_enabled(self, enabled: bool):
        """设置UI元素启用/禁用状态"""
        # 顶部按钮
        self.import_folder_btn.disabled = enabled
        self.refresh_btn.disabled = enabled
        
        # 底部按钮
        self.add_before_btn.disabled = enabled
        self.add_after_btn.disabled = enabled
        
        # 更新页面
        self.page.update()

def main(page: ft.Page):
    def on_page_load(e):
        page.window_maximized = True
        page.update()
    page.on_load = on_page_load
    app = PDFEditorApp(page)


if __name__ == "__main__":
    ft.app(target=main, view=ft.FLET_APP)