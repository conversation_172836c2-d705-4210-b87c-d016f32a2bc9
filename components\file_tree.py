import os
import asyncio
import re
import flet as ft
from utils.pdf_processor import PDFProcessor
from utils.status_manager import StatusManager

class FileTreeComponent:
    def __init__(self, page, state):
        self.page = page
        self.state = state
        self.tree = ft.Column(expand=True, spacing=5)
        self.subfolders_list = ft.ListView(expand=True, spacing=5)
        self.selected_path = None
        self.pdf_processor = PDFProcessor()
        self.processing_mode = False
        
    def create(self):
        # 注册UI控制回调
        self.state.add_ui_control_callback(self._set_processing_mode)
        return ft.Container(
            content=self.tree,
            width=200,
            expand=False,
            border=ft.border.all(1, ft.Colors.GREY_400),
            padding=5,
            border_radius=5
        )
        
    async def refresh(self, e):
        if not self.state.root_folder:
            return
            
        self.tree.controls.clear()
        self.subfolders_list.controls.clear()
        
        # 添加子文件夹
        try:
            subfolders = []
            for entry in os.scandir(self.state.root_folder):
                if entry.is_dir(follow_symlinks=False):
                    subfolders.append(entry)
            
            # 按自然排序
            subfolders.sort(key=lambda x: self.natural_sort_key(x.name))
            
            for i, entry in enumerate(subfolders, start=1):
                processed_name = re.sub(r'(\.pdf)-.*$', r'\1', entry.name)
                node = ft.ListTile(
                    title=ft.Text(processed_name),
                    leading=ft.Text(f"{i}. "),
                    selected=entry.path == self.selected_path,
                    on_click=lambda e, path=entry.path: self.page.run_task(self.handle_node_click, path)
                )
                self.subfolders_list.controls.append(node)
            
            self.tree.controls.append(self.subfolders_list)
        except Exception as e:
            self.state.status_text.value = f"扫描文件夹出错: {e}"
            self.state.status_text.color = ft.Colors.RED
        
        self.page.update()
    
    def natural_sort_key(self, s):
        return [int(text) if text.isdigit() else text.lower()
                for text in re.split('([0-9]+)', s)]
    
    async def load_folder(self, folder_path):

        
        # 查找PDF文件
        pdf_files = []
        for file in os.listdir(folder_path):
            if file.lower().endswith('origin.pdf'):
                pdf_files.append(file)
        
        if not pdf_files:
            self.state.status_text.value = "在文件夹中未找到PDF文件"
            self.state.status_text.color = ft.Colors.RED
            self.page.update()
            return
        
        # 加载第一个PDF
        pdf_files.sort(key=self.natural_sort_key)
        pdf_path = os.path.join(folder_path, pdf_files[0])
        self.state.current_pdf = pdf_path
        
        # 检查并处理PDF图片
        if not self.pdf_processor.check_images_exist(pdf_path):
            print("DEBUG: 开始处理PDF图片...")
            # 设置处理模式，禁用UI
            self.state.set_processing_mode(True)
            await self.state.status_manager.set_processing_status("正在准备处理PDF图片...", 0)
            
            try:
                success = await self.pdf_processor.process_pdf_images(
                    pdf_path, 
                    self.state.status_manager,
                    on_complete=None
                )
                
                if not success:
                    await self.state.status_manager.set_system_status(
                        "图片预处理被取消或失败", ft.Colors.RED
                    )
                    return
            finally:
                # 无论成功失败都重新启用UI
                self.state.set_processing_mode(False)
        
        # 使用状态管理器重置所有状态
        self.state.reset_all_selections()
        
        # 强制清除PDF文档引用，确保重新加载
        self.state.pdf_doc = None
        
        # 查找JSON文件
        json_path = os.path.join(folder_path, "layout.json")
        if os.path.exists(json_path):
            self.state.current_json = json_path
            await asyncio.to_thread(self.state.json_editor.load_json, json_path)
            # 通知主程序加载PDF
            await self.state.pdf_viewer.load_pdf(pdf_path)
            await self.state.status_manager.set_system_status(
                f"已加载PDF: {os.path.basename(pdf_path)}", ft.Colors.GREEN
            )
        else:
            self.state.current_json = None
            # 先加载PDF获取页数
            await self.state.pdf_viewer.load_pdf(pdf_path)
            
            # 然后初始化JSON结构
            if self.state.pdf_doc:
                page_count = self.state.pdf_doc.page_count
                self.state.json_data = {"pdf_info": [{"para_blocks": []} for _ in range(page_count)]}
                # 确保PDF查看器和JSON编辑器都同步更新
                if hasattr(self.state, 'pdf_viewer') and self.state.pdf_viewer:
                    self.state.pdf_viewer.state.json_data = self.state.json_data
                if hasattr(self.state, 'json_editor') and self.state.json_editor:
                    self.state.json_editor.render_content()
            
            await self.state.status_manager.set_system_status(
                f"已加载PDF: {os.path.basename(pdf_path)} - 未找到 layout.json 文件，已创建新文件", ft.Colors.ORANGE
            )
    
    async def handle_node_click(self, path):
        if self.processing_mode:
            print("DEBUG: 处理中，忽略点击")
            return  # 处理中禁用点击
        self.selected_path = path
        await self.load_folder(path)
        await self.refresh(None)
    
    def _set_processing_mode(self, processing: bool):
        """设置处理模式"""
        self.processing_mode = processing