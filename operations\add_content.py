import asyncio
import flet as ft

class AddContentHandler:
    def __init__(self, page, state, dialogs):
        self.page = page
        self.state = state
        self.dialogs = dialogs
    
    async def handle_before(self, e):
        if self.state.selected_block is None:
            return
        
        new_content = await self.dialogs.get_text_input(
            self.page, "新增内容", "输入要添加的文本内容:"
        )
        if not new_content:
            return
        
        # 创建新块
        new_block = {
            "type": "文本",
            "lines": [{
                "spans": [{
                    "content": new_content,
                    "type": "text"
                }]
            }],
            "bbox": [100, 100, 300, 120]
        }
        
        # 插入到选中块前面
        if self.state.selected_page < len(self.state.json_data.get('pdf_info', [])):
            page_obj = self.state.json_data['pdf_info'][self.state.selected_page]
            blocks = page_obj.get('para_blocks', [])
            blocks.insert(self.state.selected_block, new_block)
            self.state.json_editor.render_content()
            await self._save_json()
    
    async def handle_after(self, e):
        if self.state.selected_block is None:
            return
        
        new_content = await self.dialogs.get_text_input(
            self.page, "新增内容", "输入要添加的文本内容:"
        )
        if not new_content:
            return
        
        # 创建新块
        new_block = {
            "type": "文本",
            "lines": [{
                "spans": [{
                    "content": new_content,
                    "type": "text"
                }]
            }],
            "bbox": [100, 100, 300, 120]
        }
        
        # 插入到选中块后面
        if self.state.selected_page < len(self.state.json_data.get('pdf_info', [])):
            page_obj = self.state.json_data['pdf_info'][self.state.selected_page]
            blocks = page_obj.get('para_blocks', [])
            # 检查选中块索引是否有效
            if self.state.selected_block < len(blocks):
                blocks.insert(self.state.selected_block + 1, new_block)
                self.state.json_editor.render_content()
                await self._save_json()
    
    async def _save_json(self):
        if self.state.current_json:
            try:
                with open(self.state.current_json, 'w', encoding='utf-8') as f:
                    json.dump(self.state.json_data, f, ensure_ascii=False, indent=2)
                self.state.status_text.value = "JSON文件已保存"
                self.state.status_text.color = ft.Colors.GREEN
                self.page.update()
                await asyncio.sleep(2)
                self.state.status_text.value = "就绪"
                self.state.status_text.color = ft.Colors.BLUE
                self.page.update()
            except Exception as e:
                self.state.status_text.value = f"无法保存JSON文件: {e}"
                self.state.status_text.color = ft.Colors.RED
                self.page.update()