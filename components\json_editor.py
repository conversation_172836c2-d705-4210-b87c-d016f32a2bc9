import json
import asyncio
import flet as ft

class JSONEditorComponent:
    def __init__(self, page, state):
        self.page = page
        self.state = state
        # 添加状态变化监听
        self.state.add_listener(self.on_state_changed)
        # 创建滚动容器
        self.list_view = ft.ListView(
            expand=True,
            spacing=5,
            padding=5
        )
        self.last_page_index = self.state.current_page_index
        self.page.run_task(self._periodic_page_check)
        self.processing_mode = False
    
    def on_state_changed(self, **kwargs):
        """监听状态变化并更新UI"""
        if 'selected_block' in kwargs or 'selected_page' in kwargs:
            self.highlight_selected_block()
    
    def highlight_selected_block(self):
        """高亮当前选中的块"""
        current_page = self.state.current_page_index
        for i, control in enumerate(self.list_view.controls):
            if isinstance(control, ft.Container):
                # 直接使用全局状态判断选中
                is_selected = (self.state.selected_page == current_page and 
                              self.state.selected_block == i)
                control.border = ft.border.all(2, ft.Colors.BLUE if is_selected else ft.Colors.GREY_300)
                control.bgcolor = ft.Colors.BLUE_50 if is_selected else ft.Colors.WHITE
        self.page.update()
    
    def render_content(self):
        self.list_view.controls.clear()
        
        if not self.state.json_data:
            return
        
        pdf_info = self.state.json_data.get('pdf_info', [])
        current_page = self.state.current_page_index
        if current_page < len(pdf_info):
            page_obj = pdf_info[current_page]
            blocks = page_obj.get('para_blocks', [])
            
            for block_idx, block in enumerate(blocks):
                block_type = block.get('type', '').lower()
                
                # 创建块容器
                block_container = ft.Container(
                    content=ft.Column([
                        ft.Text(f"--- 第{current_page + 1}页 - 块 {block_idx + 1} ---", weight=ft.FontWeight.BOLD),
                        ft.Text(f"类型: {block_type}"),
                        # 根据块类型添加内容
                        self._get_block_content(block, block_type)
                    ]),
                    border=ft.border.all(2, ft.Colors.GREY_300),
                    bgcolor=ft.Colors.WHITE,
                    padding=10,
                    border_radius=5,
                    on_click=lambda e, idx=block_idx: self.on_block_click(e, current_page, idx)
                )
                
                self.list_view.controls.append(block_container)
        
        # 渲染后检查并高亮选中项
        self.highlight_selected_block()
        self.page.update()
    
    def _get_block_content(self, block, block_type):
        content = ft.Column(spacing=2)
        
        if block_type in ['text', 'title', 'index', 'list']:
            lines = block.get('lines', [])
            text_lines = []
            for line in lines:
                spans = line.get('spans', [])
                for span in spans:
                    if span.get('type') == 'text':
                        text_lines.append(span.get('content', ''))
                    elif span.get('type') == 'inline_equation':
                        text_lines.append(span.get('content', ''))
            
            if text_lines:
                content.controls.append(ft.Text("内容:"))
                content.controls.extend([ft.Text(line) for line in text_lines])
        
        elif block_type == 'table':
            # 表格内容处理
            block2 = block.get('blocks', [])
            html_table = []
            image_path = []
            for block22 in block2:
                lines = block22.get('lines', [])
                for line in lines:
                    spans = line.get('spans', [])
                    for span in spans:
                        if span.get('type') == 'table':
                            html_table.append(span.get('html', ''))
                            image_path.append(span.get('image_path', ''))
            
            if html_table:
                content.controls.append(ft.Text("表格:"))
                content.controls.extend([ft.Text(html) for html in html_table])
            if image_path:
                content.controls.append(ft.Text("图片路径:"))
                content.controls.extend([ft.Text(path) for path in image_path])
        
        elif block_type == 'image':
            block2 = block.get('blocks', [])
            image_path = []
            for block22 in block2:
                lines = block22.get('lines', [])
                for line in lines:
                    spans = line.get('spans', [])
                    for span in spans:
                        if span.get('type') == 'image':
                            image_path.append(span.get('image_path', ''))
            
            if image_path:
                content.controls.append(ft.Text("图片路径:"))
                content.controls.extend([ft.Text(path) for path in image_path])
        
        # 添加位置信息
        pos = block.get('bbox', [])
        if pos:
            content.controls.append(ft.Text("位置:"))
            content.controls.append(ft.Text(f"x0={pos[0]}, y0={pos[1]}, x1={pos[2]}, y1={pos[3]}"))
        
        return content
    
    def on_block_click(self, e, page_index, block_idx):
        """处理JSON块点击事件"""
        if self.processing_mode:
            return  # 处理中禁用点击
            
        # 使用状态setter方法触发监听器
        self.state.set_selected_page(page_index)
        self.state.set_selected_block(block_idx)
        if hasattr(self.state, 'status_manager'):
            async def _update_status():
                await self.state.status_manager.set_user_action_status(
                    f"已选择: 第{page_index + 1}页, 第{block_idx + 1}个内容块"
                )
            self.page.run_task(_update_status)
        else:
            self.state.status_text.value = f"已选择: 第{page_index + 1}页, 第{block_idx + 1}个内容块"
            self.state.status_text.color = ft.Colors.BLUE
        # 更新PDF视图选中状态
        if hasattr(self.state, 'pdf_viewer') and self.state.pdf_viewer:
            self.state.pdf_viewer.draw_bboxes(page_index)
        # 更新当前选中项高亮
        self.highlight_selected_block()
    
    def load_json(self, json_path):
        with open(json_path, 'r', encoding='utf-8') as f:
            self.state.json_data = json.load(f)
        # 使用状态管理器重置选中状态并重新渲染
        self.state.reset_all_selections()
        self.render_content()

    async def _periodic_page_check(self):
        while True:
            await asyncio.sleep(0.3)
            if self.state.current_page_index != self.last_page_index:
                self.last_page_index = self.state.current_page_index
                self.render_content()

    def on_focus(self, e):
        if self.processing_mode:
            return  # 处理中禁用焦点事件
            
        self.state.selected_page = self.state.current_page_index
        self.state.selected_block = 0
        if hasattr(self.state, 'status_manager'):
            async def _update_status():
                await self.state.status_manager.set_user_action_status(
                    f"已选择: 第{self.state.current_page_index + 1}页, 第1个内容块"
                )
            self.page.run_task(_update_status)
        else:
            self.state.status_text.value = f"已选择: 第{self.state.current_page_index + 1}页, 第1个内容块"
            self.state.status_text.color = ft.Colors.BLUE
        self.page.update()

    def create(self):
        # 注册UI控制回调
        self.state.add_ui_control_callback(self._set_processing_mode)
        return self.list_view

    def _set_processing_mode(self, processing: bool):
        """设置处理模式"""
        self.processing_mode = processing